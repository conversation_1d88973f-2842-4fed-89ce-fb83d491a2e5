<template>
  <a-modal
    :title="getModalTitle()"
    :visible="visible"
    :width="700"
    :maskClosable="false"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="batch-import-container" @click="handleContainerClick">
      <div class="tips-section">
        <a-alert
          message="操作提示"
          :description="tipsText"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </div>
      <div class="table-container">
        <div class="table-wrapper">
          <table class="excel-table" @paste="handlePaste" tabindex="0">
            <thead>
              <tr>
                <th class="name-column">{{ getNameColumnTitle() }}</th>
                <th class="value-column">{{ getValueColumnTitle() }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in tableData" 
                  :key="index" 
                  :class="{ 'selected-row': isRowSelected(index) }">
                <td class="name-cell">{{ item.name }}</td>
                <td 
                  class="value-cell"
                  :class="{ 'selected': isCellSelected(index, 'value'), 'editing': editingCell.row === index && editingCell.field === 'value' }"
                  @mousedown="handleMouseDown(index, 'value', $event)"
                  @mouseover="handleMouseOver(index, 'value')"
                  @mouseup="handleMouseUp"
                  @dblclick="startEdit(index, 'value')"
                >
                  <div v-if="!(editingCell.row === index && editingCell.field === 'value')" class="cell-display" @click="startEdit(index, 'value')">
                    {{ item.value || 0 }}
                  </div>
                  <input v-else ref="editInput" v-model.number="item.value" type="number" step="0.01" min="0" precision="2" class="cell-input" @focus="handleCellFocus(index, 'value')" @blur="finishEdit" @keydown="handleInputKeyDown($event, index, 'value')" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="summary-section">
        <div class="summary-item">
          <span>总数量：</span>
          <span class="summary-value">{{ tableData.length }}</span>
        </div>
        <div class="summary-item">
          <span>已填充：</span>
          <span class="summary-value">{{ filledCount }}</span>
        </div>
      </div>
      <div class="footer-actions">
        <a-button @click="handleClear" style="margin-right: 8px;">清空数据</a-button>
        <a-button @click="handleCancel" style="margin-right: 8px;">取消</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'SimulationBatchImportModal',
  props: {
    visible: { type: Boolean, default: false },
    nameList: { type: Array, default: () => [] }, // 名称列表（需水口名称、调度对象名称等）
    sourceType: { type: String, default: '' }, // 'waterDemandValue', 'waterLevel', 'flow'
  },
  data() {
    return {
      tableData: [],
      selectedRange: { start: -1, end: -1 },
      selectedFields: [],
      isSelecting: false,
      focusedCell: { row: -1, field: '' },
      editingCell: { row: -1, field: '' },
      selectionStartField: '',
    }
  },
  computed: {
    tipsText() {
      return '1. 单击或双击数值单元格可直接编辑数值；2. 支持粘贴Excel数据；3. 拖动鼠标可选择多行；4. 点击保存按钮将数据应用到主表格';
    },
    filledCount() {
      return this.tableData.filter(item => item.value > 0).length;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initTableData()
        this.$nextTick(() => {
          const table = this.$el.querySelector('.excel-table')
          if (table) table.focus()
        })
      }
    },
    nameList: {
      handler() {
        if (this.visible) this.initTableData()
      },
      deep: true
    }
  },
  mounted() {
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    getModalTitle() {
      if (this.sourceType === 'waterDemandValue') {
        return '批量导入需水量';
      } else if (this.sourceType === 'waterLevel') {
        return '批量导入水位';
      } else if (this.sourceType === 'flow') {
        return '批量导入流量';
      }
      return '批量导入';
    },
    
    getNameColumnTitle() {
      if (this.sourceType === 'waterDemandValue') {
        return '需水口';
      } else if (this.sourceType === 'waterLevel') {
        return '调度对象';
      } else if (this.sourceType === 'flow') {
        return '调度对象';
      }
      return '名称';
    },
    
    getValueColumnTitle() {
      if (this.sourceType === 'waterDemandValue') {
        return '需水量(万m³)';
      } else if (this.sourceType === 'waterLevel') {
        return '水位(m)';
      } else if (this.sourceType === 'flow') {
        return '流量(m³/s)';
      }
      return '数值';
    },
    
    initTableData() {
      this.tableData = this.nameList.map(name => ({
        name,
        value: 0
      }));
      this.selectedRange = { start: -1, end: -1 };
      this.selectedFields = [];
      this.selectionStartField = '';
    },
    
    handleMouseDown(index, field, event) {
      event.preventDefault()
      this.isSelecting = true
      this.selectedRange = { start: index, end: index }
      this.selectionStartField = field
      this.selectedFields = [field]
      document.addEventListener('selectstart', this.preventSelection)
    },
    
    handleMouseOver(index, field) {
      if (this.isSelecting) {
        this.selectedRange.end = index
        this.selectedFields = ['value'];
      }
    },
    
    handleMouseUp() {
      this.isSelecting = false
      document.removeEventListener('selectstart', this.preventSelection)
    },
    
    preventSelection(e) {
      e.preventDefault()
      return false
    },
    
    handleCellFocus(index, field) {
      this.focusedCell = { row: index, field }
      if (this.selectedRange.start === -1) {
        this.selectedRange = { start: index, end: index }
        this.selectedFields = [field]
        this.selectionStartField = field
      }
    },
    
    startEdit(index, field) {
      if (this.isSelecting) return
      this.editingCell = { row: index, field }
      this.focusedCell = { row: index, field }
      this.$nextTick(() => {
        const input = this.$refs.editInput
        if (input && input.length) {
          const targetInput = Array.isArray(input) ? input[0] : input
          targetInput.focus()
          targetInput.select()
        }
      })
    },
    
    finishEdit() {
      this.editingCell = { row: -1, field: '' }
      this.focusedCell = { row: -1, field: '' }
    },
    
    handleInputKeyDown(event, index, field) {
      switch (event.key) {
        case 'Enter':
          event.preventDefault()
          this.finishEdit()
          if (index < this.tableData.length - 1) this.startEdit(index + 1, field)
          break
        case 'Escape':
          event.preventDefault()
          this.finishEdit()
          break
        case 'Tab':
          event.preventDefault()
          this.finishEdit()
          if (!event.shiftKey && index < this.tableData.length - 1) this.startEdit(index + 1, field)
          else if (event.shiftKey && index > 0) this.startEdit(index - 1, field)
          break
        case 'ArrowDown':
          if (!event.ctrlKey) {
            event.preventDefault()
            this.finishEdit()
            if (index < this.tableData.length - 1) this.startEdit(index + 1, field)
          }
          break
        case 'ArrowUp':
          if (!event.ctrlKey) {
            event.preventDefault()
            this.finishEdit()
            if (index > 0) this.startEdit(index - 1, field)
          }
          break
      }
    },
    
    isRowSelected(index) {
      const start = Math.min(this.selectedRange.start, this.selectedRange.end)
      const end = Math.max(this.selectedRange.start, this.selectedRange.end)
      return index >= start && index <= end && start !== -1
    },
    
    isCellSelected(index, field) {
      return this.isRowSelected(index) && this.selectedFields.includes(field)
    },
    
    handlePaste(event) {
      event.preventDefault()
      const clipboardData = event.clipboardData || window.clipboardData
      const pastedText = clipboardData.getData('text')
      if (!pastedText.trim()) {
        this.$message.warning('粘贴内容为空')
        return
      }
      
      if (this.selectedRange.start === -1) {
        this.selectedRange = { start: 0, end: this.tableData.length - 1 }
        this.selectedFields = ['value'];
      }
      
      this.processPastedData(pastedText)
    },
    
    processPastedData(text) {
      const lines = text.trim().split(/\r?\n/)
      const values = []
      
      lines.forEach(line => {
        const cells = line.split(/\t|,|\s+/).filter(cell => cell.trim())
        if (cells.length > 0) {
          values.push(parseFloat(cells[0]) || 0);
        }
      })
      
      if (values.length === 0) {
        this.$message.warning('未识别到有效的数值数据')
        return
      }
      
      const start = Math.min(this.selectedRange.start, this.selectedRange.end)
      const end = Math.max(this.selectedRange.start, this.selectedRange.end)
      let valueIndex = 0
      
      for (let i = start; i <= end && i < this.tableData.length; i++) {
        if (valueIndex < values.length) {
          this.tableData[i].value = values[valueIndex];
          valueIndex++
        }
      }
      
      this.$message.success(`已成功粘贴 ${Math.min(values.length, end - start + 1)} 行数据`)
    },
    
    handleKeyDown(event) {
      if (!this.visible) return;
      
      if (this.editingCell.row !== -1) return;
      
      if ((event.key === 'Delete' || event.key === 'Backspace') && this.selectedRange.start !== -1) {
        event.preventDefault();
        const start = Math.min(this.selectedRange.start, this.selectedRange.end);
        const end = Math.max(this.selectedRange.start, this.selectedRange.end);
        
        for (let i = start; i <= end; i++) {
          if (this.selectedFields.includes('value')) {
            this.tableData[i].value = 0;
          }
        }
      }
    },
    
    handleClear() {
      this.$confirm({
        title: '确认清空',
        content: '确定要清空所有数据吗？',
        onOk: () => {
          this.tableData.forEach(item => {
            item.value = 0;
          });
          this.clearSelection();
          this.$message.success('已清空所有数据');
        }
      })
    },
    
    handleSave() {
      const hasData = this.tableData.some(item => item.value > 0);
      if (!hasData) {
        this.$message.warning('请先填入数据');
        return;
      }
      
      const result = this.tableData.map(item => ({
        name: item.name,
        [this.sourceType]: item.value
      }));
      
      this.$emit('save', result);
      this.handleCancel();
    },
    
    handleCancel() {
      this.$emit('update:visible', false)
      this.clearSelection();
      this.isSelecting = false
      this.focusedCell = { row: -1, field: '' }
      this.editingCell = { row: -1, field: '' }
    },
    
    handleContainerClick(event) {
      const target = event.target;
      
      if (target.closest('.excel-table')) {
        return;
      }
      
      if (target.closest('button')) {
        return;
      }
      
      this.clearSelection();
    },
    
    clearSelection() {
      this.selectedRange = { start: -1, end: -1 };
      this.selectedFields = [];
      this.selectionStartField = '';
    }
  }
}
</script>

<style lang="less" scoped>
.batch-import-container {
  .tips-section {
    margin-bottom: 16px;
  }
  .table-container {
    max-height: 400px;
    overflow: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 16px;
    .table-wrapper {
      position: relative;
    }
    .excel-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      outline: none;
      th, td {
        border: 1px solid #e8e8e8;
        padding: 8px 12px;
        text-align: left;
        position: relative;
      }
      th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
        position: sticky;
        top: 0;
        z-index: 10;
      }
      .name-column {
        width: 50%;
        min-width: 150px;
      }
      .value-column {
        width: 50%;
        min-width: 150px;
      }
      .name-cell {
        background-color: #f5f5f5;
        color: #666;
        font-family: 'Consolas', 'Monaco', monospace;
      }
      .value-cell {
        cursor: pointer;
        user-select: none;
        transition: all 0.2s ease;
        position: relative;
        &:hover {
          background-color: rgba(22, 93, 255, 0.1);
        }
        &.selected {
          background-color: rgba(22, 93, 255, 0.2);
          border-color: #165DFF;
          box-shadow: inset 0 0 0 2px #165DFF;
        }
        &.editing {
          background-color: #fff;
          border-color: #165DFF;
          box-shadow: inset 0 0 0 2px #165DFF;
        }
        .cell-display {
          width: 100%;
          height: 100%;
          padding: 4px;
          font-size: 12px;
          text-align: center;
          min-height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: text;
          &:hover {
            background-color: rgba(22, 93, 255, 0.05);
          }
        }
        .cell-input {
          width: 100%;
          border: none;
          outline: none;
          background: transparent;
          padding: 4px;
          font-size: 12px;
          text-align: center;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1;
        }
      }
      .selected-row {
        .name-cell {
          background-color: rgba(22, 93, 255, 0.1);
        }
      }
      .value-cell.selected {
        box-shadow: inset 0 0 0 2px #165DFF;
      }
    }
  }
  .summary-section {
    display: flex;
    gap: 24px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;
    .summary-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      span:first-child {
        color: #666;
        margin-right: 4px;
      }
      .summary-value {
        font-weight: 600;
        color: #262626;
      }
    }
  }
  .footer-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;
  }
}

.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  &:hover {
    background: #a8a8a8;
  }
}
</style>
