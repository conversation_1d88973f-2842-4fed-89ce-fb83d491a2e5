import Vue from 'vue'
import ShuiZhaInputPopupContent from './ShuiZhaInputPopupContent.vue'
import ShuiZhaLabelPopupContent from './ShuiZhaLabelPopupContent.vue'
import ShuiKuPopupContent from './ShuiKuPopupContent.vue'
import mapboxgl from 'mapbox-gl'

export function mapboxShuiZhaPopup(mapIns, item) {
  const PopupContentItem = Vue.extend(ShuiZhaInputPopupContent)

  const vIns = new PopupContentItem({
    propsData: { item },
  })
  vIns.$mount()
  const popupTemp = vIns.$el

  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: [0, 0],
  }

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(popupTemp).addTo(mapIns)
}

export function mapboxShuiZhaLabelPopup(mapIns, item) {
  const PopupContentItem = Vue.extend(ShuiZhaLabelPopupContent)

  const vIns = new PopupContentItem({
    propsData: { item },
  })
  vIns.$mount()
  const popupTemp = vIns.$el

  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: [0, 0],
  }

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(popupTemp).addTo(mapIns)
}

export function mapboxShuikuPopup(mapIns, item) {
  const PopupContentItem = Vue.extend(ShuiKuPopupContent)

  const vIns = new PopupContentItem({
    propsData: { item },
  })
  vIns.$mount()
  const popupTemp = vIns.$el

  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: [0, 0],
  }

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(popupTemp).addTo(mapIns)
}
