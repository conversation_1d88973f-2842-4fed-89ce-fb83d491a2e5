<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="720"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">调度令编码：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdCode }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度令名称：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度类型：</label>
            <span class="common-value-text">
              {{ objDetail?.dispatchTypeName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">计划工作时间：</label>
            <span class="common-value-text">
              {{ objDetail?.planStartDate && objDetail?.planEndDate ? `${objDetail.planStartDate} ~ ${objDetail.planEndDate}` : '-' }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">工程信息</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px;height: 200px">
            <VxeTable
              ref="vxeProjectTableRef"
              :height="200"
              :isShowTableHeader="false"     
              :columns="projectColumns"
              :tableData="objDetail?.projectList || []"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>

        <!-- 审核模式的额外内容 -->
        <template v-if="mode === 'audit'">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model ref="auditForm" :model="auditForm" :rules="auditRules">
              <a-form-model-item label="审批意见" prop="auditResult">
                <a-radio-group v-model="auditForm.auditResult" @change="onAuditResultChange">
                  <a-radio :value="1">同意</a-radio>
                  <a-radio :value="0">驳回</a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="其他说明" prop="remark">
                <a-textarea 
                  v-model="auditForm.remark" 
                  placeholder="请输入其他说明" 
                  :rows="4"
                  allow-clear 
                />
              </a-form-model-item>
            </a-form-model>
          </a-col>
        </template>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button v-if="mode === 'audit'" type="primary" @click="handleAudit">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import moment from 'moment'
  import { getDispatchInstructionById } from '../services'

  export default {
    name: 'AuditModal',
    components: { AntModal, VxeTable },
    props: [],
    data() {
      return {
        formTitle: '审核调度指令',
        modalLoading: false,
        open: false,
        mode: 'audit', // audit
        objDetail: {},
        auditForm: {
          auditResult: 1,  // 设置默认值为同意
          remark: '',      // 其他说明
        },
        auditRules: {
          auditResult: [{ required: true, message: '请选择审批意见', trigger: 'change' }],
          remark: [],
        },
        submitLoading: false,
        projectColumns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '负责人',
            field: 'wardUserName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
        ],
      }
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
        // 重置表单
        if (this.$refs.auditForm) {
          this.$refs.auditForm.resetFields()
        }
      },

      /** 按钮操作 */
      handle(row, mode = 'audit') {
        this.open = true
        this.mode = mode
        this.modalLoading = true

        // 设置标题
        this.formTitle = '审核调度指令'

        // 重置审核表单，并设置默认值为同意
        this.auditForm = {
          auditResult: 1,  // 设置默认值为同意(1)
          remark: '',
        }
        this.updateRemarkRule()

        // 获取详情数据
        getDispatchInstructionById(row.runCmdId).then(res => {
          if (res.success && res.data) {
            this.objDetail = res.data
          } else {
            this.$message.error(res.message || '获取详情失败')
          }
          this.modalLoading = false
        }).catch(err => {
          console.error('获取详情失败:', err)
          this.$message.error('获取详情失败')
          this.modalLoading = false
        })
      },

      onAuditResultChange(e) {
        this.auditForm.auditResult = e.target.value
        this.updateRemarkRule()
      },

      handleAudit() {
        this.$refs.auditForm.validate(valid => {
          if (valid) {
            this.submitLoading = true

            // 通知父组件进行审核操作
            this.$emit('auditResult', this.objDetail, this.auditForm)
            this.open = false
            this.$emit('close')
            this.$refs.auditForm.resetFields()
            this.submitLoading = false
          }
        })
      },

      updateRemarkRule() {
        if (this.auditForm.auditResult === 0) {
          this.auditRules.remark = [{ required: true, message: '请输入其他说明', trigger: 'blur' }]
        } else {
          this.auditRules.remark = []
        }
        this.$nextTick(() => {
          if (this.$refs.auditForm) {
            this.$refs.auditForm.clearValidate(['remark'])
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 20px;
  }
</style>
