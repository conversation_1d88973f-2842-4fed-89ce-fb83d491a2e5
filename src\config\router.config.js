// eslint-disable-next-line
import { UserLayout, BasicLayout } from '@/layouts'

/**
 * 工作台
 */
export const indexRouterMap = [
  {
    path: '/index',
    name: 'index',
    component: 'Workbench',
    meta: { title: '工作台', keepAlive: true, icon: '工作台', noCache: false },
  },
]
/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login'),
      },
    ],
  },
  {
    path: '/404',
    name: '404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404'),
  },
  {
    path: '/403',
    name: '403',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/403'),
  },
  {
    path: '/500',
    name: '500',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/500'),
  },

  {
    path: '/demo',
    component: BasicLayout,
    redirect: '/demo/demo',
    hidden: true,
    children: [
      {
        path: 'demo',
        name: 'demo',
        meta: { title: '测试页', keepAlive: true, icon: 'home', noCache: false },
        component: () => import(/* webpackChunkName: "demo" */ '@/views/demo/index'),
      },
    ],
  },

  {
    path: '/process-line-detail',
    name: 'ProcessLineDetail',
    component: () =>
      import(/* webpackChunkName: "ProcessLineDetail" */ '@/views/water-rain/process-line/process-line-detail/index'),
    hidden: true,
    meta: { title: '水雨情过程线内嵌', whiteList: true },
  },
  {
    path: '/project-detail',
    name: 'ProjectDetail',
    component: () =>
      import(/* webpackChunkName: "ProjectDetail" */ '@/views/project/project-list/project-detail/index'),
    hidden: true,
    meta: { title: '项目详情内嵌', whiteList: true },
  },
  {
    path: '/ai-warn',
    name: 'AIWarn',
    component: () => import(/* webpackChunkName: "AIWarn" */ '@/views/early-warning/monitor/AIWarn/index.vue'),
    hidden: true,
    meta: { title: 'AI预警监控', whiteList: true },
  },
]
