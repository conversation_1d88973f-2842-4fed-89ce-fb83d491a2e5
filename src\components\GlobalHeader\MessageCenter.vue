<template>
  <a-drawer
    title="消息中心"
    placement="right"
    :closable="true"
    :visible="visible"
    :width="400"
    @close="onClose"
    :body-style="{ padding: 0, height: '100%' }"
  >
    <div class="message-container">
      <div class="message-list" v-if="!loading">
      <div
        v-for="(message, index) in messageList"
        :key="message.id || index"
        class="message-item"
      >
        <!-- 预报预警信息 -->
        <div v-if="message.type === 'forecast'" class="message-content">
          <div class="message-header">
            <div class="message-title">【{{ message.warningGroup }}-{{ message.warningTarget }}】</div>
            <span class="read-status" :class="{ 'unread': message.msgStatus !== 2 }">
              {{ message.msgStatus === 2 ? '已读' : '未读' }}
            </span>
          </div>
          <div class="message-info">
            <div class="info-row">
              <span class="label">预警时间：</span>
              <span>{{ formatDateTime(message.warningTime) }}</span>
            </div>
            <div class="info-row" v-if="message.warningCode">
              <span class="label">预警编号：</span>
              <span>{{ message.warningCode }}</span>
            </div>
            <div class="info-row" v-if="message.warningMessage">
              <span class="label">预警消息：</span>
              <span>{{ message.warningMessage }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button 
              type="primary" 
              size="small" 
              @click="handleViewMessage(message)"
            >
              {{ message.msgStatus === 2 ? '已查看' : '查看' }}
            </a-button>
          </div>
        </div>

        <!-- 巡检任务 -->
        <div v-else-if="message.type === 'inspection'" class="message-content">
          <div class="message-header">
            <div class="message-title">【{{ message.taskName }}】</div>
            <span class="read-status" :class="{ 'unread': message.msgStatus !== 2 }">
              {{ message.msgStatus === 2 ? '已读' : '未读' }}
            </span>
          </div>
          <div class="message-info">
            <div class="info-row" v-if="message.planStartTime">
              <span class="label">计划开始时间：</span>
              <span>{{ formatDateTime(message.planStartTime) }}</span>
            </div>
            <div class="info-row" v-if="message.planEndTime">
              <span class="label">计划结束时间：</span>
              <span>{{ formatDateTime(message.planEndTime) }}</span>
            </div>
            <div class="info-row" v-if="message.taskCode">
              <span class="label">任务编码：</span>
              <span>{{ message.taskCode }}</span>
            </div>
            <div class="info-row" v-if="message.inspectionScope">
              <span class="label">巡检范围：</span>
              <span>{{ message.inspectionScope }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button 
              type="primary" 
              size="small" 
              @click="handleViewMessage(message)"
              :disabled="message.msgStatus === 2"
            >
              {{ message.msgStatus === 2 ? '已查看' : '查看' }}
            </a-button>
          </div>
        </div>

        <!-- 应急抢修任务 -->
        <div v-else-if="message.type === 'emergency'" class="message-content">
          <div class="message-header">
            <div class="message-title">【应急抢修-{{ message.reportUnit }}】</div>
            <span class="read-status" :class="{ 'unread': message.msgStatus !== 2 }">
              {{ message.msgStatus === 2 ? '已读' : '未读' }}
            </span>
          </div>
          <div class="message-info">
            <div class="info-row">
              <span class="label">提报时间：</span>
              <span>{{ formatDateTime(message.reportTime) }}</span>
            </div>
            <div class="info-row" v-if="message.workOrderCode">
              <span class="label">工单编号：</span>
              <span>{{ message.workOrderCode }}</span>
            </div>
            <div class="info-row" v-if="message.emergencyLocation">
              <span class="label">应急部位：</span>
              <span>{{ message.emergencyLocation }}</span>
            </div>
            <div class="info-row" v-if="message.problemDescription">
              <span class="label">问题描述：</span>
              <span>{{ message.problemDescription }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button 
              type="primary" 
              size="small" 
              @click="handleViewMessage(message)"
              :disabled="message.msgStatus === 2"
            >
              {{ message.msgStatus === 2 ? '已查看' : '查看' }}
            </a-button>
          </div>
        </div>

        <!-- 用水调度 -->
        <div v-else-if="message.type === 'dispatch'" class="message-content">
          <div class="message-header">
            <div class="message-title">【用水调度-{{ message.dispatchType }}】</div>
            <span class="read-status" :class="{ 'unread': message.msgStatus !== 2 }">
              {{ message.msgStatus === 2 ? '已读' : '未读' }}
            </span>
          </div>
          <div class="message-info">
            <div class="info-row" v-if="message.planDispatchDate">
              <span class="label">计划调度日期：</span>
              <span>{{ formatDateTime(message.planDispatchDate) }}</span>
            </div>
            <div class="info-row" v-if="message.dispatchCode">
              <span class="label">调度单编号：</span>
              <span>{{ message.dispatchCode }}</span>
            </div>
            <div class="info-row" v-if="message.planWaterAmount">
              <span class="label">计划调度水量：</span>
              <span>{{ message.planWaterAmount }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button 
              type="primary" 
              size="small" 
              @click="handleViewMessage(message)"
              :disabled="message.msgStatus === 2"
            >
              {{ message.msgStatus === 2 ? '已查看' : '查看' }}
            </a-button>
          </div>
        </div>
      </div>

        <!-- 空状态 -->
        <div v-if="messageList.length === 0" class="empty-state">
          <a-empty description="暂无消息" />
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-else class="loading-state">
        <a-spin size="large">
          <div style="height: 200px; display: flex; align-items: center; justify-content: center;">
            加载中...
          </div>
        </a-spin>
      </div>
    </div>
  </a-drawer>
</template>

<script>
import { getMessageList, markMessageAsRead } from '@/api/message'
import { SocketClient } from '@/utils/sockClient.js'

export default {
  name: 'MessageCenter',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      messageList: [],
      loading: false,
      socketIns: null
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.userId) {
        this.loadMessages()
        this.initWebSocket()
      } else if (!newVal) {
        this.disconnectWebSocket()
      }
    }
  },
  beforeDestroy() {
    this.disconnectWebSocket()
  },
  methods: {
    async loadMessages() {
      if (!this.userId) {
        this.$message.warning('用户ID不存在，无法加载消息')
        return
      }
      
      this.loading = true
      try {
        const response = await getMessageList(this.userId)
        if (response.success && response.code === 200) {
          this.messageList = this.formatMessages(response.data || [])
        } else {
          this.$message.error('获取消息列表失败: ' + (response.message || '未知错误'))
        }
      } catch (error) {
        console.error('获取消息列表失败:', error)
        this.$message.error('获取消息列表失败')
      } finally {
        this.loading = false
      }
    },
    
    formatMessages(rawMessages) {
      return rawMessages.map(msg => {
        let parsedContent = {}
        
        // 根据msgType确定消息类型和解析方式
        let type = 'forecast' // 默认类型
        switch (msg.msgType) {
          case 1:
            type = 'forecast'
            // 预报预警信息content是字符串格式，需要特殊解析
            parsedContent = this.parseForecastContent(msg.content || '')
            break
          case 2:
            type = 'inspection'
            parsedContent = this.parseJSONContent(msg.content)
            break
          case 3:
            type = 'emergency'
            parsedContent = this.parseJSONContent(msg.content)
            break
          case 4:
            type = 'dispatch'
            parsedContent = this.parseJSONContent(msg.content)
            break
          default:
            parsedContent = this.parseJSONContent(msg.content)
        }
        
        return {
          id: msg.id,
          type,
          msgStatus: msg.msgStatus, // 1已发送 2已读 3发送失败
          sentTime: msg.sentTime,
          readTime: msg.readTime,
          rawData: parsedContent, // 保存原始解析数据
          
          // 预报预警信息
          warningGroup: parsedContent.warningGroup || '预警分组',
          warningTarget: parsedContent.warningTarget || '预警对象名称', 
          warningTime: parsedContent.warningTime || msg.sentTime,
          warningCode: parsedContent.warningCode || '',
          warningMessage: parsedContent.warningMessage || '',
          
          // 巡检任务
          taskName: parsedContent.taskName || '巡检任务名称',
          planStartTime: parsedContent.planStartTime || '',
          planEndTime: parsedContent.planEndTime || '',
          taskCode: parsedContent.taskCode || '',
          inspectionScope: parsedContent.inspectionScope || '',
          
          // 应急抢修任务  
          reportUnit:  parsedContent.serialNumber || '应急抢修',
          reportTime: parsedContent.createdTime || msg.sentTime,
          workOrderCode: parsedContent.serialNumber || '',
          emergencyLocation: parsedContent.emergencyLocation || '',
          problemDescription: parsedContent.content || '',
          
          // 用水调度
          dispatchType: parsedContent.serialNumber || '调度类型名称',
          planDispatchDate: parsedContent.waterDate || '',
          dispatchCode: parsedContent.serialNumber || '',
          planWaterAmount: parsedContent.waterValue ? (parsedContent.waterValue + '万m³') : ''
        }
      })
    },
    
    // 解析JSON格式的content
    parseJSONContent(content) {
      try {
        return JSON.parse(content || '{}')
      } catch (error) {
        console.warn('解析JSON消息内容失败:', error)
        return {}
      }
    },
    
    // 解析预报预警信息的字符串格式content
    parseForecastContent(content) {
      if (!content) return {}
      
      try {
        // 解析预警编码和时间
        const parts = content.split(',')
        let warningCode = ''
        let warningTime = ''
        let warningMessage = ''
        let warningGroup = ''
        let warningTarget = ''
        
        if (parts.length >= 2) {
          // 提取预警编码
          const codeMatch = parts[0].match(/预警编码(.+)/)
          if (codeMatch) {
            warningCode = codeMatch[1].trim()
          }
          
          // 提取预警时间
          const timeMatch = parts[1].match(/预警时间:(.+)/)
          if (timeMatch) {
            warningTime = timeMatch[1].trim()
          }
          
          // 提取预警对象（第三部分可能包含预警对象信息）
          if (parts.length >= 3) {
            const targetInfo = parts[2].trim()
            // 尝试从目标信息中提取分组和对象名称
            if (targetInfo.includes('水库')) {
              warningGroup = '水库监测'
              warningTarget = targetInfo
            } else if (targetInfo.includes('站')) {
              warningGroup = '站点监测'
              warningTarget = targetInfo
            } else {
              warningGroup = '监测预警'
              warningTarget = targetInfo
            }
          }
        }
        
        // 提取预警消息（剩余内容作为预警消息）
        const messageStart = content.indexOf('\n')
        if (messageStart !== -1) {
          warningMessage = content.substring(messageStart + 1)
        }
        
        return {
          warningCode,
          warningTime,
          warningGroup,
          warningTarget,
          warningMessage
        }
      } catch (error) {
        console.warn('解析预警消息内容失败:', error)
        return {
          warningMessage: content
        }
      }
    },
    
    async handleViewMessage(message) {
      if (message.msgStatus === 2) {
        // 已读状态，直接跳转到对应页面
        this.navigateToMessageDetail(message)
        return
      }
      
      try {
        const response = await markMessageAsRead(message.id)
        if (response.success && response.code === 200) {
          // 更新本地状态
          const index = this.messageList.findIndex(msg => msg.id === message.id)
          if (index !== -1) {
            this.$set(this.messageList[index], 'msgStatus', 2)
            this.$set(this.messageList[index], 'readTime', new Date().toISOString())
          }
          // this.$message.success('消息已标记为已读')
          
          // 跳转到对应页面
          this.navigateToMessageDetail(message)
        } else {
          this.$message.error('标记消息已读失败: ' + (response.message || '未知错误'))
        }
      } catch (error) {
        console.error('标记消息已读失败:', error)
        this.$message.error('标记消息已读失败')
      }
    },
    
    // 跳转到消息详情页面
    navigateToMessageDetail(message) {
      let routePath = ''
      
      switch (message.type) {
        case 'forecast':
          routePath = '/earlyWarning/monitor'
          break
        case 'inspection':
          routePath = '/patrol/line-patrol/line-patrol-task'
          break
        case 'emergency':
          routePath = '/operation-maintenance/emergency-repair'
          break
        case 'dispatch':
          routePath = '/water-resource/use-water'
          break
        default:
          console.warn('未知的消息类型:', message.type)
          return
      }
      
      try {
        this.$router.push(routePath)
        // 关闭抽屉
        this.onClose()
      } catch (error) {
        console.error('路由跳转失败:', error)
        this.$message.error('页面跳转失败')
      }
    },
    
    // 初始化WebSocket连接
    initWebSocket() {
      if (this.socketIns) {
        this.socketIns.disconnect()
      }
      
      this.socketIns = new SocketClient()
      this.socketIns.connect('/topic/sys/msg', response => {
        if (response.type === 'newMessage') {
          // 有新消息时重新加载消息列表
          this.loadMessages()
        }
      })
    },
    
    // 断开WebSocket连接
    disconnectWebSocket() {
      if (this.socketIns) {
        this.socketIns.disconnect()
        this.socketIns = null
      }
    },
    
    onClose() {
      this.$emit('close')
    },
    
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      try {
        const date = new Date(dateTime)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit', 
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return dateTime
      }
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .ant-drawer-wrapper-body {
  overflow: hidden !important;
  // padding-bottom: 120px;
}
.message-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 16px 80px 16px;
  
  .message-item {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #cbcccc;
    &:last-child {
      margin-bottom: 0;
    }

    .message-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .message-title {
          font-weight: 600;
          font-size: 14px;
          color: #262626;
          line-height: 1.4;
          flex: 1;
        }
        
        .read-status {
          font-size: 12px;
          color: #52c41a;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          padding: 2px 6px;
          border-radius: 4px;
          white-space: nowrap;
          margin-left: 8px;
          
          &.unread {
            color: #ff4d4f;
            background: #fff2f0;
            border-color: #ffccc7;
          }
        }
      }

      .message-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;
          font-size: 13px;
          line-height: 1.4;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #8c8c8c;
            min-width: 80px;
            flex-shrink: 0;
          }

          span:last-child {
            color: #595959;
            flex: 1;
            word-break: break-all;
          }
        }
      }

      .message-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>
