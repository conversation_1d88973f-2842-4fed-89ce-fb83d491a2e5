<template>
  <div class="container">
    <div class="header">
      <div class="name">{{ item?.siteName }}</div>
      <a-icon type="close" style="cursor: pointer" @click="item.onPopupClose(item)" />
    </div>

    <div class="indicator">
      <div class="label">
        <span style="color:#1d2129;">{{ "流量: " + Number(item?.flow).toFixed(2) + "m³/s"}} </span>
      </div>
      <div class="label" style="margin-top: -4px;">
        <span style="color:#1d2129;">{{ "水位: " + Number(item?.wlv).toFixed(2) + "m"}}</span>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PopupContent',
    props: ['item'],
    data() {
      return {}
    }
  }
</script>
<style lang="less">
  .mapboxgl-popup-content {
    padding: 0;
  }
</style>
<style lang="less" scoped>
  .container {
    width: 100px;
    max-height: 106px;
    position: relative;
    display: flex;
    flex-direction: column;
    .header {
      background: #5384fe;
      font-weight: 500;
      color: #FFF;
      line-height: 15px;
      font-size: 10px;
      padding: 2px 2px;
      display: flex;
      align-items: center;
      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .name {
        flex: 1;
        margin: 0 5px 0 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicator {
      display: flex;
      flex-direction: column;
      margin-top: -2px;
      padding: 1px 7px;
      .label {
        font-size: 8px;
        color: #4E5969;
      }
      .value {
        color: #1d2129;
      }
    }
  }
</style>
