<template>
  <div style="height: 100%; display: flex; flex-direction: column; position: relative">
    <div
      v-if="!displayInfoOptions.length"
      style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center"
    >
      <a-empty />
    </div>

    <div v-else class="detail-content">
      <div class="detail-tab">
        <a-tabs v-model="tabVal" type="line" :tab-position="'left'" :style="{ height: '100%' }" @change="onTabChange">
          <a-tab-pane
            v-for="(ele, i) in displayInfoOptions"
            :key="ele.key"
            :tab="projectInfo.includes(ele.key) ? ele.option1 : ele.value"
          >
            <!-- 圩区 -->
            <Polder v-if="ele.key === 'projectInfoPolder'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水闸 -->
            <Sluice
              v-if="ele.key === 'projectInfoSluice'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 泵站 -->
            <Pump
              v-if="ele.key === 'projectInfoPump'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 闸站 -->
            <Gate
              v-if="ele.key === 'projectInfoGate'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 划界确权 -->
            <Demarcation
              ref="demarcationRef"
              v-if="ele.key === 'demarcation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 工程文件预览 -->
            <FileViewList
              v-if="
                ele.key === 'appearance' ||
                ele.key === 'registration' ||
                ele.key === 'safetyAppraisal' ||
                ele.key === 'label'
              "
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 管理单位 -->
            <ManageUnit
              v-if="ele.key === 'unitManagement'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :projectName="modalTitle"
              :isMap="isMap"
            />

            <!-- 应急管理 -->
            <EmergencyManage
              v-if="ele.key === 'emergencyResponse'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 安全管理 -->
            <SafetyManage
              v-if="ele.key === 'safetyInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 工程检查 -->
            <EngineInspection
              v-if="ele.key === 'inspectionEngineering'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 控制运行 -->
            <Scheduling
              v-if="ele.key === 'controlOperation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />

            <!-- 维修养护 -->
            <MaintenanceUpkeep
              v-if="ele.key === 'maintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isMap="isMap"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">
  import Demarcation from '../modules/DetailModal/Demarcation.vue'
  import FileViewList from '../modules/DetailModal/FileViewList.vue'

  import Polder from '../modules/DetailModal/polder/index.vue'
  import Sluice from '../modules/DetailModal/sluice/index.vue'
  import Pump from '../modules/DetailModal/pump/index.vue'
  import Gate from '../modules/DetailModal/gate/index.vue'
  import ManageUnit from '../modules/DetailModal/manageUnit/index.vue'
  import EmergencyManage from '../modules/DetailModal/emergency-manage/index.vue'
  import SafetyManage from '../modules/DetailModal/safety-manage/index.vue'
  import EngineInspection from '../modules/DetailModal/engine-inspection/index.vue'
  import Scheduling from '../modules/DetailModal/scheduling/index.vue'
  import MaintenanceUpkeep from '../modules/DetailModal/maintenance-upkeep/index.vue'

  import { ACCESS_TOKEN } from '@/store/mutation-types'
  import storage from 'store'
  import QueryString from 'query-string'
  import { getDisplayCodes } from '../services'
  import { getOptions } from '@/api/common'

  const searchObj = QueryString.parse(decodeURIComponent(location.search))
  console.log('🚀~searchObj~>', searchObj)

  if (searchObj?.token) {
    storage.set(ACCESS_TOKEN, searchObj.token, 7 * 24 * 60 * 60 * 1000)
  }
  export default {
    name: 'ProjectDetail',
    components: {
      Demarcation,
      FileViewList,

      Polder,
      Sluice,
      Pump,
      Gate,
      ManageUnit,
      EmergencyManage,
      SafetyManage,
      EngineInspection,
      Scheduling,
      MaintenanceUpkeep,
    },
    data() {
      return {
        isMap: true,
        modalTitle: '',
        tabVal: '',
        displayInfoOptions: [],
        recordInfo: {},
        projectInfo: ['projectInfoPolder', 'projectInfoPump', 'projectInfoSluice', 'projectInfoGate'], // 基本信息key集合
      }
    },
    computed: {},
    created() {
      console.log('isMap base', this.isMap)
      // 模拟DetailModal的handleDetail方法
      this.recordInfo = { projectId: searchObj.objectId, projectName: '项目详情' }
      this.modalTitle = this.recordInfo.projectName

      getDisplayCodes({ projectId: this.recordInfo.projectId }).then(resp => {
        if (!resp.data?.length) {
          this.$message.info('无展示信息')
          return
        }
        getOptions('displayInfoTypes').then(res => {
          this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]
          this.tabVal = this.displayInfoOptions[0]?.key
        })
      })
    },
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      onFullScreen() {
        this.$refs.demarcationRef?.[0].onFullScreen()
      },
    },
    mounted() {},
  }
</script>

<style lang="less" scoped>
  .detail-content {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #eef0f3 !important;

    .detail-tab {
      height: 100%;

      ::v-deep .ant-tabs-content {
        height: 100%;
        padding-left: 0px;
        border-left: none;
        .ant-tabs-tabpane-active {
          height: 100%;
        }
      }
      ::v-deep .ant-tabs.ant-tabs-left {
        background-color: #fff;
        .ant-tabs-left-bar {
          border-right: 10px solid #eef0f3;
        }
      }
    }
  }
</style>
