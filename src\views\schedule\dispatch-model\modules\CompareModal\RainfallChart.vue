<template>
  <div style="height: 100%; position: relative; display: flex; flex-direction: column;">
    <div style="height: 30px; padding: 4px 0; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
      <h4 style="font-weight: bold; margin: 0;">雨量对比</h4>
    </div>
    
    <!-- 图表滚动容器 -->
    <div 
      ref="scrollContainer"
      style="flex: 1; overflow-x: auto; overflow-y: hidden; border: 1px solid #f0f0f0; border-radius: 4px;"
    >
      <div 
        ref="chartContainer" 
        style="height: 100%; min-width: 800px;"
      ></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RainfallChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true,
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },

    updateChart() {
      if (!this.chart || !this.chartData.length) return

      // 计算图表宽度，确保X轴不重叠
      const dataLength = this.chartData[0]?.data.length || 0
      const minWidth = Math.max(800, dataLength * 80)

      // 设置容器宽度
      this.$refs.chartContainer.style.width = minWidth + 'px'
      
      // 分离柱状图和折线图数据
      const barSeries = this.chartData.filter(item => item.type === 'bar')
      const lineSeries = this.chartData.filter(item => item.type === 'line')
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: (params) => {
            let result = params[0].axisValue + '<br/>'
            params.forEach(param => {
              const unit = param.seriesName.includes('累计') ? 'mm' : 'mm'
              result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`
            })
            return result
          }
        },
        legend: {
          data: this.chartData.map(item => item.name),
          top: 10,
          left: 'left'
        },
        grid: {
          left: 60,
          right: 60,
          bottom: 80,
          top: 50,
          containLabel: false
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.chartData[0]?.data.map(item => item[0]) || [],
          axisLabel: {
            formatter: function(value) {
              const parts = value.split(' ')
              return parts[0] + '\n' + parts[1]
            },
            interval: 0,
            rotate: 0,
            fontSize: 10,
            margin: 15,
            lineHeight: 16
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '时段雨量(mm/h)',
            nameLocation: 'middle',
            nameGap: 40,
            nameTextStyle: {
              fontSize: 12
            },
            position: 'left'
          },
          {
            type: 'value',
            name: '累计降雨量(mm)',
            nameLocation: 'middle',
            nameGap: 40,
            nameTextStyle: {
              fontSize: 12
            },
            position: 'right'
          }
        ],
        series: [
          // 柱状图系列
          ...barSeries.map((item, index) => ({
            name: item.name,
            type: 'bar',
            yAxisIndex: 0,
            data: item.data.map(d => d[1]),
            itemStyle: {
              color: index === 0 ? '#5470c6' : '#91cc75'
            },
            barWidth: '20%'
          })),
          // 折线图系列
          ...lineSeries.map((item, index) => ({
            name: item.name,
            type: 'line',
            yAxisIndex: 1,
            data: item.data.map(d => d[1]),
            smooth: false,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: index === 0 ? '#ee6666' : '#fac858',
              width: 2
            },
            itemStyle: {
              color: index === 0 ? '#ee6666' : '#fac858'
            }
          }))
        ]
      }
      
      this.chart.setOption(option)
      
      this.$nextTick(() => {
        this.chart.resize()
      })
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
  }
}
</script>

<style lang="less" scoped>
</style>
