import request from '@/utils/request'

/**
 * 获取未读消息数量
 * @param {string} userId - 用户ID
 * @returns {Promise} 响应数据
 */
export function getUnreadCount(userId) {
  return request({
    url: '/system/sysMsgNotify/unreadCount',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { userId },
  })
}

/**
 * 获取所有消息列表
 * @param {string} userId - 用户ID
 * @returns {Promise} 响应数据
 */
export function getMessageList(userId) {
  return request({
    url: '/system/sysMsgNotify/list',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { userId },
  })
}

/**
 * 标记消息为已读
 * @param {string|number} messageId - 消息ID
 * @returns {Promise} 响应数据
 */
export function markMessageAsRead(messageId) {
  return request({
    url: '/system/sysMsgNotify/read',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { messageId },
  })
}