<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="事件编号">
        <a-input
          v-model="queryParam.eventCode"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="响应等级">
        <a-select
          show-search
          placeholder="请选择"
          v-model="queryParam.resLevel"
          option-filter-prop="children"
        >
          <a-select-option :value="1">I级</a-select-option>
          <a-select-option :value="2">II级</a-select-option>
          <a-select-option :value="3">III级</a-select-option>
          <a-select-option :value="4">IV级</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="启动时间">
        <a-range-picker
          allow-clear
          :value="startTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
import FormDetails from "./modules/FormDetails.vue";
import VxeTable from "@/components/VxeTable";
import VxeTableForm from "@/components/VxeTableForm";
import moment from "moment";
import { getEmergencyResponsePage } from '@/api/emergency-response';

export default {
  name: "EmergencyResponse",
  components: {
    VxeTable,
    VxeTableForm,
    FormDetails,
  },
  data() {
    return {
      isChecked: false,
      showFormDetails: false,
      startTimes: [],

      list: [],
      tableTitle: "应急响应",
      loading: false,
      total: 0,
      selectIds: [],
      queryParam: {
        eventCode: "",
        resLevel: null,
        beginTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10,
        sort: [],
      },
      columns: [
        {
          type: "seq",
          title: "序号",
          align: "center",
          width: 50,
          slots: {
            default: ({ row, rowIndex }) => {
              return (
                rowIndex +
                1 +
                (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              );
            },
          },
        },
        {
          title: "事件编号",
          field: "eventCode",
          align: "center",
        },
        {
          title: "响应等级",
          field: "resLevel",
          align: "center",
          slots: {
            default: ({ row, rowIndex }) => {
              const levelMap = { 1: 'I', 2: 'II', 3: 'III', 4: 'IV' };
              return (levelMap[row.resLevel] || row.resLevel) + "级";
            },
          },
        },
        {
          title: "响应条件",
          field: "resCondition",
          align: "center",
        },
        {
          title: "启动时间",
          field: "startTime",
          align: "center",
          slots: {
            default: ({ row }) => {
              return row.startTime
                ? moment(row.startTime).format("YYYY-MM-DD HH:mm:ss")
                : "";
            },
          },
        },
        {
          title: "负责人",
          field: "chargeName",
          align: "center",
        },
        {
          title: "操作",
          field: "operate",
          width: 80,
          align: "left",
          slots: {
            default: ({ row, rowIndex }) => {
              return (
                <span>
                  <a onClick={() => this.handleDetails(row)}>查看</a>
                </span>
              );
            },
          },
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    adaptPageSizeChange(pageSize) {
      this.queryParam.pageSize = pageSize;
      this.getList();
    },
    onRangeChange(value, startTimes) {
      this.startTimes = value;
      if (startTimes.length == 0) {
        this.queryParam.beginTime = null;
        this.queryParam.endTime = null;
        return;
      }
      this.queryParam.beginTime = startTimes[0]
        ? moment(startTimes[0]).format("YYYY-MM-DD HH:mm:ss")
        : null;
      this.queryParam.endTime = startTimes[1]
        ? moment(startTimes[1]).format("YYYY-MM-DD HH:mm:ss")
        : null;
    },
    /** 查询列表 */
    async getList() {
      this.showFormDetails = false;
      this.loading = true;
      this.selectChange({ records: [] });

      try {
        const response = await getEmergencyResponsePage(this.queryParam);
        if (response.success) {
          this.list = response.data.data || [];
          this.total = response.data.total || 0;
        } else {
          this.$message.error(response.message || '查询失败');
          this.list = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('查询应急响应数据失败:', error);
        this.$message.error('查询失败，请稍后重试');
        this.list = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },
    // 多选框选中
    selectChange(valObj) {
      this.selectIds = valObj.records.map((item) => item.id);
      this.isChecked = !!valObj.records.length;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParam.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParam = {
        eventCode: "",
        resLevel: null,
        beginTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10,
        sort: [],
      };
      this.startTimes = [];
      this.handleQuery();
    },

    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.queryParam.pageNum = currentPage;
      this.queryParam.pageSize = pageSize;
      this.getList();
    },
    // 导出
    handleExport() {
      this.$message.info("导出功能待实现");
    },
    /* 查看 */
    handleDetails(record) {
      this.showFormDetails = true;
      this.$nextTick(() => this.$refs.formDetailsRef.details(record));
    },

    // 操作完成后
    onOperationComplete() {
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped></style>