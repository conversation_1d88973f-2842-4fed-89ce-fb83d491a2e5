<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="调度令编号">
        <a-input v-model="queryParam.cmdCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="调度令名称">
        <a-input v-model="queryParam.cmdName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="调度方案">
        <a-select
          show-search
          allow-clear
          v-model="queryParam.dispatchId"
          :options="dispatchOptions"
          placeholder="请选择"
          option-filter-prop="children"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()" v-if="hasAddPermission">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          @ok="onOperationComplete"
          @close="showForm = false"
          :dispatchOptions="dispatchOptions"
        />
        <AuditModal
          v-if="showAudit"
          ref="auditRef"
          @close="showAudit = false"
          @auditResult="handleAuditResult"
        />
        <DetailModal
          v-if="showDetail"
          ref="detailRef"
          @close="showDetail = false"
          @receiveResult="handleReceiveResult"
        />
        <!-- 操作指令详情弹窗 -->
        <OptCmdDetail v-if="showOptDetail" ref="optDetailRef" @close="showOptDetail = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import FormDrawer from './modules/FormDrawer.vue'
  import AuditModal from './modules/AuditModal.vue'
  import DetailModal from './modules/DetailModal.vue'
  import ExpandTable from './modules/ExpandTable.vue'
  import OptCmdDetail from '../operation-instruction/modules/OptCmdDetail.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import { getPage, getDispatchOptions, auditDispatchInstruction, receiveDispatchInstruction } from './services'

  export default {
    name: 'DispatchInstruction',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
      AuditModal,
      DetailModal,
      ExpandTable,
      OptCmdDetail,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: Number,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        isChecked: false,
        showForm: false,
        showAudit: false,
        showDetail: false,
        showOptDetail: false,
        dispatchOptions: [],
        list: [],
        tableTitle: '调度指令',
        loading: false,
        total: 0,
        selectIds: [],
        hasAddPermission: true,
        queryParam: {
          dispatchId: null,
          cmdName: '',
          cmdCode: '',
          keyword: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },

        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            type: 'expand',
            width: 30,
            fixed: 'left',
            slots: {
              content: ({ row, rowIndex }) => {
                return <ExpandTable row={row} onViewOperateCmd={this.handleViewOperateCmd} />
              },
            },
          },
          {
            title: '调度令编号',
            field: 'cmdCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '调度令名称',
            field: 'cmdName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '计划工作时间',
            field: 'planWorkTime',
            minWidth: 180,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.planStartDate && row.planEndDate) {
                  return `${row.planStartDate} ~ ${row.planEndDate}`
                }
                return '-'
              },
            },
          },
          {
            title: '工作负责人',
            field: 'wardUserName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '当前状态',
            field: 'statusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                const statusMap = {
                  0: { text: '审批中', color: '#1890ff' },
                  1: { text: '下发中', color: '#52c41a' },
                  2: { text: '已驳回', color: '#ff4d4f' },
                  3: { text: '已完成', color: '#8c8c8c' }
                }
                const status = statusMap[row.statusCode] || { text: '未知', color: '#8c8c8c' }
                return <span style={`color: ${status.color}`}>{status.text}</span>
              },
            },
          },
          {
            title: '操作',
            field: 'action',
            width: 200,
            fixed: 'right',
            slots: {
              default: ({ row }) => {
                const buttons = []

                // 审核按钮（状态为0显示）
                if (row.statusCode === 0) {
                  buttons.push(<a onClick={() => this.handleAudit(row)}>审核</a>)
                }

                // 接收按钮（状态为1显示）
                if (row.statusCode === 1) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleReceive(row)}>接收</a>)
                }

                // 查看按钮（状态为2或3显示）
                if (row.statusCode === 2 || row.statusCode === 3) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleView(row)}>查看</a>)
                }

                // 修改按钮（状态为0或2显示）
                if (row.statusCode === 0 || row.statusCode === 2) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleEdit(row)}>修改</a>)
                }

                // 复制按钮（固定显示）
                if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                buttons.push(<a onClick={() => this.handleCopy(row)}>复制</a>)

                // 删除按钮（固定显示）
                buttons.push(<a-divider type='vertical' />)
                buttons.push(<a onClick={() => this.handleDeleteSingle(row)}>删除</a>)

                return <span>{buttons}</span>
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      this.loadDispatchOptions()
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showAudit = false
        this.showDetail = false
        this.showOptDetail = false
        this.loading = true
        this.selectChange({ records: [] })

        const params = Object.entries(this.queryParam).reduce((acc, [key, value]) => {
          acc[key] = value === '' ? null : value
          return acc
        }, {})

        getPage(params).then(res => {
          if (res.success) {
            this.list = res.data.data || []
            this.total = res.data.total || 0
          } else {
            this.$message.error(res.message || '查询失败')
          }
          this.loading = false
        }).catch(err => {
          console.error('查询列表失败:', err)
          this.$message.error('查询失败')
          this.loading = false
        })
      },

      /** 加载调度方案选项 */
      loadDispatchOptions() {
        getDispatchOptions().then(res => {
          if (res.success && res.data && res.data.data) {
            this.dispatchOptions = res.data.data.map(item => ({
              label: item.caseName,
              value: item.chSimId
            }))
          }
        }).catch(err => {
          console.error('加载调度方案选项失败:', err)
        })
      },

      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.runCmdId)
        this.isChecked = !!valObj.records.length
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          dispatchId: null,
          cmdName: '',
          cmdCode: '',
          keyword: '',
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 自适应页面大小变化
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.queryParam.pageNum = 1
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showForm = true
        let row = { action: '新增' }
        this.$nextTick(() => this.$refs.formRef.handle(row))
      },

      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        record.action = '修改'
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },

      /* 复制 */
      handleCopy(record) {
        this.showForm = true
        record.action = '复制'
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },

      /* 审核 */
      handleAudit(record) {
        this.showAudit = true
        this.$nextTick(() => this.$refs.auditRef.handle(record, 'audit'))
      },

      /* 接收 */
      handleReceive(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'receive'))
      },

      /* 查看 */
      handleView(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'view'))
      },

      /* 删除单个 */
      handleDeleteSingle(record) {
        this.$confirm({
          title: '确认删除',
          content: `确定要删除调度令"${record.cmdName}"吗？`,
          onOk: () => {
            // TODO: 实现删除接口调用
            this.$message.success('删除成功')
            this.getList()
          }
        })
      },

      /* 批量删除 */
      handleDelete() {
        if (!this.selectIds.length) {
          this.$message.warning('请选择要删除的数据')
          return
        }
        this.$confirm({
          title: '确认删除',
          content: `确定要删除选中的 ${this.selectIds.length} 条记录吗？`,
          onOk: () => {
            // TODO: 实现批量删除接口调用
            this.$message.success('删除成功')
            this.getList()
          }
        })
      },

      /* 操作完成回调 */
      onOperationComplete() {
        this.getList()
      },

      /* 审核结果回调 */
      handleAuditResult(record, auditForm) {
        const data = {
          runCmdId: record.runCmdId,
          auditResult: auditForm.auditResult,
          remark: auditForm.remark || ''
        }

        auditDispatchInstruction(data).then(res => {
          if (res.success) {
            this.$message.success('审核完成')
            this.getList()
          } else {
            this.$message.error(res.message || '审核失败')
          }
        }).catch(err => {
          console.error('审核失败:', err)
          this.$message.error('审核失败')
        })
      },

      /* 接收结果回调 */
      handleReceiveResult(record) {
        receiveDispatchInstruction(record.runCmdId).then(res => {
          if (res.success) {
            this.$message.success('接收完成')
            this.getList()
          } else {
            this.$message.error(res.message || '接收失败')
          }
        }).catch(err => {
          console.error('接收失败:', err)
          this.$message.error('接收失败')
        })
      },

      /* 查看操作指令详情 */
      handleViewOperateCmd(record) {
        this.showOptDetail = true
        this.$nextTick(() => this.$refs.optDetailRef.handle(record))
      },
    }
  }
</script>

<style lang="less" scoped>
// .common-table-page {
//     width: 100%;
//   height: 100%;

//   :deep(.ant-form-item) {
//     margin-bottom: 16px;
//   }
// }
</style>