<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案名称：</label>
            <span class="common-value-text">{{ form.emergencyPlanName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案类型：</label>
            <span class="common-value-text">{{ emergencyPlanTypeFormat(form.emergencyPlanType) }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">批复时间：</label>
            <span class="common-value-text">{{ form.replyTime }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">负责人：</label>
            <span class="common-value-text">{{ form.chargeName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text" :title="form.projectName">{{ form.projectName }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <label class="common-label-text">批文：</label>
            <div
              class="file-item"
              v-for="(el, i) in form.approvalAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div>
            <label class="common-label-text">预案文件：</label>
            <div class="file-item" v-for="(el, i) in form.planAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>

        <!-- 应急预案事件区块 -->
        <a-col :lg="24" :md="24" :sm="24" v-if="groupedEmergencyEvents && Object.keys(groupedEmergencyEvents).length > 0">
          <div class="emergency-events-container">
            <h3 class="section-title">应急预案事件</h3>
            
            <div 
              v-for="(events, level) in groupedEmergencyEvents" 
              :key="level" 
              class="response-level-block"
            >
              <div class="response-level-container">
                <h4 class="response-level-title">{{ getLevelName(level) }}</h4>
                
                <div 
                  v-for="(event, eventIndex) in events" 
                  :key="event.id || eventIndex" 
                  class="event-item"
                >
                  <div class="event-content">
                    <div class="content-row">
                      <label class="content-label">响应内容：</label>
                      <div class="content-text">{{ event.resContent || '暂无内容' }}</div>
                    </div>
                    
                    <div class="content-row" v-if="event.warnContent">
                      <label class="content-label">预警内容：</label>
                      <div class="content-text">{{ event.warnContent }}</div>
                    </div>
                    
                    <div class="content-row" v-if="event.resCondition">
                      <label class="content-label">响应条件：</label>
                      <div class="content-text">{{ event.resCondition }}</div>
                    </div>
                    
                    <div class="content-row" v-if="event.recUserNames && event.recUserNames.length > 0">
                      <label class="content-label">接收人：</label>
                      <div class="content-text">{{ event.recUserNames.join(', ') }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- <div class="form-item-title">配置模板内容</div> -->
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getPlanById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'emergencyTypeOptions'],
    data() {
      return {
        modalLoading: false,
        formTitle: '',
        form: {
          approvalAttaches: [],
          chargeName: '',
          emergencyPlanId: null,
          emergencyPlanName: '',
          emergencyPlanType: null,
          emergencyResEvents: [],
          planAttaches: [],
          projectId: null,
          replyTime: '',
        },
        open: false,
        rules: {},
      }
    },
    computed: {
      // 按响应级别分组的应急事件
      groupedEmergencyEvents() {
        if (!this.form.emergencyResEvents || this.form.emergencyResEvents.length === 0) {
          return {}
        }
        
        const grouped = {}
        this.form.emergencyResEvents.forEach(event => {
          const level = event.resLevel
          if (!grouped[level]) {
            grouped[level] = []
          }
          grouped[level].push(event)
        })
        
        // 按级别排序
        const sortedGrouped = {}
        Object.keys(grouped)
          .sort((a, b) => parseInt(a) - parseInt(b))
          .forEach(key => {
            sortedGrouped[key] = grouped[key]
          })
        
        return sortedGrouped
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 获取响应级别名称
      getLevelName(level) {
        const levelMap = {
          1: 'I级响应',
          2: 'II级响应',
          3: 'III级响应',
          4: 'IV级响应'
        }
        return levelMap[parseInt(level)] || `${level}级响应`
      },
      // 响应等级格式化
      emergencyPlanTypeFormat(value) {
        if (value) {
          return this.emergencyTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 年份格式化
      yearFormat(value) {
        if (value) {
          return moment(value).format('YYYY')
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      downLoad(url) {
        window.open(url)
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          this.modalLoading = true
          getPlanById({ emergencyPlanId: row.emergencyPlanId }).then(res => {
            if (res.code == 200) {
              this.form = {
                ...res.data
              }
              this.form.emergencyPlanType = String(this.form.emergencyPlanType)
              this.modalLoading = false
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .emergency-events-container {
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #111;
      margin-bottom: 20px;
      margin-top: 20px;
    }
  }

  .response-level-block {
    margin-bottom: 24px;
    
    .response-level-container {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 16px;
      background-color: #fafafa;
    }
    
    .response-level-title {
      font-size: 16px;
      font-weight: 600;
      color: #111;
      margin-bottom: 16px;
      margin-top: 0;
    }
    
    .response-content {
      margin-bottom: 16px;
      
      .response-label {
        display: inline-block;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .response-content-text {
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        min-height: 40px;
        line-height: 1.5;
        color: #262626;
      }
    }
    
    .response-config {
      .config-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
        margin-top: 0;
      }
      
      .config-table {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        
        .table-header {
          display: flex;
          background-color: #fafafa;
          border-bottom: 1px solid #d9d9d9;
          
          .header-cell {
            font-weight: 600;
            color: #262626;
          }
        }
        
        .table-row {
          display: flex;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
        }
        
        .table-cell {
          padding: 8px 12px;
          border-right: 1px solid #f0f0f0;
          display: flex;
          align-items: center;
          
          &:first-child {
            width: 180px;
          }
          
          &:nth-child(2),
          &:nth-child(3) {
            flex: 1;
          }
          
          &:last-child {
            border-right: none;
          }
          
          .rule-config-text {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
          }
          
          .config-content-text {
            color: #262626;
          }
        }
      }
    }
  }

  .event-item {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .event-content {
    .content-row {
      display: flex;
      margin-bottom: 12px;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .content-label {
        min-width: 80px;
        font-weight: 500;
        color: #262626;
        margin-right: 12px;
      }

      .content-text {
        flex: 1;
        color: #595959;
        line-height: 1.5;
        word-break: break-word;
      }
    }
  }

  .rules-config {
    margin-top: 16px;
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;

    .config-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      margin-top: 0;
      color: #262626;
    }

    .rules-table {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fff;
      
      .table-header {
        display: flex;
        background-color: #fafafa;
        border-bottom: 1px solid #d9d9d9;
        
        .header-cell {
          font-weight: 600;
          color: #262626;
        }
      }
      
      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
      }
      
      .table-cell {
        padding: 8px 12px;
        border-right: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        flex: 1;
        
        &:last-child {
          border-right: none;
        }
        
        .rule-text {
          color: #262626;
          font-size: 13px;
        }
      }
    }
  }
</style>
