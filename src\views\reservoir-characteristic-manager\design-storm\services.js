import request from '@/utils/request'

// 分页查询设计暴雨（含细节）
export function getCstDesignStormPage() {
  return request({
    url: '/custom/cstDesignStorm/page',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//查看设计暴雨详情（含细节）
export function getWaterSupplyDetails(params) {
  return request({
    url: `/custom/cstDesignStorm/${params.id}`,
    method: 'get',
    headers: {
      'Content-Type': 'x-www-form-urlencoded',
    },
  })
}

//新增设计暴雨（含细节）
export function addCstDesignStorm(data) {
  return request({
    url: '/custom/cstDesignStorm',
    method: 'post',
    data,
  })
}
//新增设计暴雨（含细节）
export function updateCstDesignStorm(data) {
  return request({
    url: `/custom/cstDesignStorm/${data.id}`,
    method: 'post',
    data,
  })
}

//删除设计暴雨
export function delCstDesignStorm(params) {
  return request({
    url: `/custom/cstDesignStorm/${params.id}`,
    method: 'DELETE',
    headers: {
      'Content-Type': 'x-www-form-urlencoded',
    },
  })
}
