<template>
  <div style="flex: 1; width: 100%; height: 100%; display: flex; flex-direction: column;">
    <!-- 加载进度显示 -->
    <div v-if="showProgress" class="progress-container"
      style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 40px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <div class="step-name">{{ processInfo.stepname || '正在处理...' }}</div>
        <div class="progress-wrapper">
          <a-progress :percent="processInfo.process || 0" :show-info="true" stroke-color="#1890ff"
            :format="percent => `${percent}%`" />
        </div>
        <div v-if="processInfo.remaintime > 0" class="remain-time">
          预计剩余时间：{{ processInfo.remaintime }} 秒
        </div>
      </div>
    </div>

    <!-- 完成状态显示 -->
    <div v-else-if="showCompleted" class="completed-container"
      style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 40px;">
      <div style="text-align: center;">
        <a-icon type="check-circle" class="success-icon" />
        <div class="success-text">方案已生成</div>
        <div class="view-result-btn" @click="handleViewResult">
          <span style="color: #86909C; font-size: 14px;">点击查看</span><span
            style="color: #165DFF; font-size: 14px; font-weight: bold;">调度结果</span>
        </div>
      </div>
    </div>

    <!-- 错误状态显示 -->
    <div v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <!-- 调度结果面板 -->
    <div v-else-if="!!chSimId && showResultPanel"
      style="flex: 1; height: 100%; width: 100%; display: flex; overflow: hidden;">
      <!-- 左侧闸门树 -->
      <div class="left-panel"
        style="width: 300px; border-right: 1px solid #e8e8e8; padding: 16px; display: flex; flex-direction: column; overflow: hidden;">
        <div class="panel-title">闸门选择</div>
        <div class="tree-container" style="flex: 1; overflow: hidden;">
          <MultiTree :treeOptions="treeOptions" :currentKeys="selectedKeys" @check="handleTreeCheck"
            @onTreeMounted="handleTreeMounted" />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content" style="flex: 1; display: flex; flex-direction: column; padding: 16px;">
        <!-- 调度过程图表 -->
        <div class="chart-section" style="flex: 1; margin-bottom: 16px;">
          <div class="panel-title">调度过程</div>
          <div class="chart-container" style="height: 300px; width: 100%;">
            <LineEchart :height="'300px'" :width="'100%'" :dataSource="chartDataSource" :custom="chartCustom"
              ref="flowChartRef" />
          </div>
        </div>
        
        <!-- 供需平衡表格 -->
        <div class="table-section" style="flex: 1;">
          <div class="panel-title">供需平衡</div>
          <div class="table-container" style="height: 300px;">
            <vxe-table 
              ref="supplyDemandTableRef"
              :data="supplyDemandData"
              :height="'100%'"
              :loading="tableLoading"
              stripe
              border="inner"
              class="supply-demand-table">
              <vxe-column field="deptName" title="用水户" min-width="120" align="center"></vxe-column>
              <vxe-column field="waterDemandValue" title="需水量（万m³）" min-width="120" align="center">
                <template #default="{ row }">
                  {{ row.waterDemandValue || 0 }}
                </template>
              </vxe-column>
              <vxe-column field="supplyValue" title="供水量（万m³）" min-width="120" align="center">
                <template #default="{ row }">
                  {{ row.supplyValue || 0 }}
                </template>
              </vxe-column>
              <vxe-column field="degreeValue" title="完成度（%）" min-width="120" align="center">
                <template #default="{ row }">
                  {{ (row.degreeValue * 100).toFixed(2) }}%
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
import { getModelRunProcess, getChGateList, getModelFlowRes, getModelSupplyRes } from '../../services'
import MultiTree from '@/components/TreeGeneral/multiTree.vue'
import { LineEchart } from '@/components/Echarts'
import { VxeTable, VxeColumn } from 'vxe-table'

export default {
  name: 'ScheduleResult',
  props: ['baseInfo', 'projectFlows', 'chSimId'],
  components: {
    MultiTree,
    LineEchart,
    VxeTable,
    VxeColumn,
  },
  data() {
    return {
      loading: false,
      errorInfo: null,
      processInfo: {
        isover: 0,
        mid: '',
        moditime: '',
        msg: '',
        process: 0,
        remaintime: 0,
        stepname: ''
      },
      pollTimer: null,
      showResultPanel: false,

      // 树相关数据
      treeOptions: {
        dataSource: [],
        replaceFields: {
          key: 'key',
          title: 'title',
          children: 'children'
        }
      },
      selectedKeys: [],
      treeData: [], // 原始树数据

      // 图表相关数据
      chartDataSource: [],
      chartCustom: {
        shortValue: false,
        dataZoom: true,
        showAreaStyle: false,
        xLabel: '时间',
        yLabel: '调度流量(m³/s)',
        legend: true,
        grid: {
          left: '10%',
          right: '10%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
      },

      // 供需平衡表格数据
      supplyDemandData: [],
      tableLoading: false,
      
      // 当前选中的工程编码
      currentProjectCode: '',
    }
  },
  computed: {
    showProgress() {
      return this.chSimId && this.processInfo.isover === 0
    },
    showCompleted() {
      return this.chSimId && this.processInfo.isover === 1 && this.processInfo.process === 100 && !this.showResultPanel
    }
  },
  watch: {
    chSimId: {
      handler(newVal) {
        if (newVal) {
          this.startPolling()
        }
      },
      immediate: true
    }
  },
  created() {
    this.$emit('update:isDisabledBtn', true)
    this.errorInfo = null
  },
  mounted() { },
  beforeDestroy() {
    this.stopPolling()
  },
  methods: {
    // 开始轮询获取进度
    startPolling() {
      if (!this.chSimId) return

      this.getProgress()
      this.pollTimer = setInterval(() => {
        this.getProgress()
      }, 6000) // 每6秒轮询一次
    },

    // 停止轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
    },

    // 获取模型运行进度
    async getProgress() {
      try {
        const response = await getModelRunProcess({ chSimId: this.chSimId })
        console.log('模型进度响应:', response)

        if (response.data) {
          this.processInfo = response.data

          // 如果完成了，停止轮询并启用按钮
          if (response.data.isover === 1 && response.data.process === 100) {
            this.stopPolling()
            this.$emit('update:isDisabledBtn', false)
          }

          // 如果有错误信息，显示错误
          if (response.data.msg) {
            this.errorInfo = response.data.msg
            this.stopPolling()
          }
        }
      } catch (error) {
        console.error('获取模型进度失败:', error)
        this.errorInfo = '获取模型进度失败，请重试'
        this.stopPolling()
      }
    },

    // 查看调度结果 - 获取闸门列表
    async handleViewResult() {
      console.log('查看调度结果，chSimId:', this.chSimId)

      try {
        // 获取闸门列表数据
        const response = await getChGateList({ chSimId: this.chSimId })
        console.log('闸门列表响应:', response)

        if (response.data && response.data.length > 0) {
          this.buildTreeData(response.data)
          this.loadSupplyDemandData() // 加载供需平衡数据
          this.showResultPanel = true
        } else {
          this.errorInfo = '获取闸门列表失败，无数据'
        }
      } catch (error) {
        console.error('获取闸门列表失败:', error)
        this.errorInfo = '获取闸门列表失败，请重试'
      }
    },

    // 构建树数据
    buildTreeData(gateListData) {
      if (!gateListData || gateListData.length === 0) return

      const treeData = []

      // 根据新的数据结构构建树
      gateListData.forEach(channel => {
        const channelNode = {
          key: channel.projectCode,
          title: channel.projectName,
          type: 'category',
          projectCode: channel.projectCode,
          children: []
        }

        // 添加渠道下的工程项目
        if (channel.chProjects && channel.chProjects.length > 0) {
          channel.chProjects.forEach(project => {
            channelNode.children.push({
              key: `${channel.projectCode}_${project.projectCode}`,
              title: project.projectName,
              type: 'data',
              projectCode: project.projectCode,
              projectName: project.projectName,
              channelCode: channel.projectCode,
              channelName: channel.projectName
            })
          })
        }

        if (channelNode.children.length > 0) {
          treeData.push(channelNode)
        }
      })

      this.treeData = treeData
      this.treeOptions.dataSource = treeData
    },

    // 树节点选择事件
    handleTreeCheck(keys, nodes) {
      this.selectedKeys = keys
      this.updateChart(nodes)
    },

    // 树挂载完成事件
    handleTreeMounted(keys, nodes) {
      this.selectedKeys = keys
      if (nodes && nodes.length > 0) {
        this.updateChart(nodes)
      }
    },

    // 更新图表数据
    async updateChart(selectedNodes) {
      if (!selectedNodes || selectedNodes.length === 0) {
        this.chartDataSource = []
        this.currentProjectCode = ''
        return
      }

      const chartData = []
      const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa541c']

      try {
        for (let i = 0; i < selectedNodes.length; i++) {
          const node = selectedNodes[i]
          if (node.type === 'data' && node.projectCode) {
            this.currentProjectCode = node.projectCode
            
            // 调用新的流量接口
            const response = await getModelFlowRes({
              chSimId: this.chSimId,
              projectCode: node.projectCode
            })
            
            if (response.data && response.data.length > 0) {
              // 构建时间序列数据
              const timeSeriesData = response.data.map(item => [
                item.tm, // 时间
                item.flowValue // 调度流量
              ])
              
              // 按时间排序
              timeSeriesData.sort((a, b) => new Date(a[0]) - new Date(b[0]))
              
              chartData.push({
                name: node.projectName,
                color: colors[i % colors.length],
                data: timeSeriesData
              })
            }
          }
        }
        
        this.chartDataSource = chartData
      } catch (error) {
        console.error('获取流量数据失败:', error)
        this.chartDataSource = []
      }
    },

    // 加载供需平衡数据
    async loadSupplyDemandData() {
      if (!this.chSimId) return
      
      this.tableLoading = true
      try {
        const response = await getModelSupplyRes({ chSimId: this.chSimId })
        console.log('供需平衡响应:', response)
        
        if (response.data && Array.isArray(response.data)) {
          this.supplyDemandData = response.data
        } else {
          this.supplyDemandData = []
        }
      } catch (error) {
        console.error('获取供需平衡数据失败:', error)
        this.supplyDemandData = []
      } finally {
        this.tableLoading = false
      }
    },

    save() {
      this.$emit('saveData', true)
    },
  },
}
</script>

<style lang="less" scoped>
.progress-container {
  .step-name {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .progress-wrapper {
    width: 400px;
    margin: 0 auto;

    ::v-deep .ant-progress-text {
      color: #1890ff;
      font-weight: 500;
    }
  }

  .remain-time {
    margin-top: 10px;
    color: #666;
    font-size: 14px;
  }
}

.completed-container {
  .success-icon {
    font-size: 48px;
    color: #52c41a;
    margin-bottom: 20px;
  }

  .success-text {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .view-result-btn {
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}
.tree-container {
  overflow-y: auto !important;
}
.left-panel {
  background: #fafafa;
}

.chart-container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  background: #fff;
}

.table-container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  background: #fff;
}

.supply-demand-table {
  ::v-deep .vxe-table--header-wrapper {
    background: #fafafa;
  }
  
  ::v-deep .vxe-table--body-wrapper {
    .vxe-body--row:hover {
      background-color: #f5f5f5;
    }
  }
}

.chart-section,
.table-section {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.right-content {
  overflow: hidden;
}
</style>