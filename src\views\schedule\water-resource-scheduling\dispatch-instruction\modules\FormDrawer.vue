<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度令编码" prop="cmdCode">
              <a-input v-model="form.cmdCode" placeholder="请输入" allow-clear @input="onCmdCodeInput" />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度令名称" prop="cmdName">
              <a-input v-model="form.cmdName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度方案">
              <a-select
                show-search
                allow-clear
                v-model="form.dispatchId"
                :options="dispatchOptions"
                placeholder="请选择"
                option-filter-prop="children"
                @change="onDispatchChange"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="调度类型" prop="dispatchTypeCode">
              <a-select
                show-search
                allow-clear
                v-model="form.dispatchTypeCode"
                :options="dispatchTypeOptions"
                placeholder="请选择"
                option-filter-prop="children"
              />
            </a-form-model-item>
          </a-col>



          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="计划工作时间" prop="planTime">
              <a-range-picker
                allow-clear
                showTime
                format="YYYY-MM-DD HH:mm"
                valueFormat="YYYY-MM-DD HH:mm"
                :placeholder="['开始时间', '结束时间']"
                v-model="form.planTime"
                :disabledDate="disabledDate"
                @change="onRangeChange"
              />
            </a-form-model-item>
          </a-col>


          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item prop="projectList">
              <div class="title">工程信息</div>
            <div class="project-table-container">
              <div class="table-header">
                <a-button type="primary" @click="addProjectRow" style="margin-bottom: 16px;">
                  <a-icon type="plus" />
                  新增工程
                </a-button>
              </div>
              <a-table
                :columns="projectColumns"
                :data-source="form.projectList"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: '100%' }"
              >
                <template slot="projectName" slot-scope="text, record, index">
                  <a-select
                    v-model="record.projectId"
                    :options="projectOptions"
                    placeholder="请选择工程"
                    style="width: 100%"
                    @change="(value) => handleProjectSelectChange(value, index)"
                  />
                </template>
                <template slot="projectManager" slot-scope="text, record, index">
                  <a-select
                    v-model="record.wardUserId"
                    :options="record.userOptions || []"
                    placeholder="请选择负责人"
                    style="width: 100%"
                  />
                </template>
                <template slot="remark" slot-scope="text, record, index">
                  <a-input
                    v-model="record.remark"
                    placeholder="请输入备注"
                  />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a-button
                    type="link"
                    danger
                    @click="removeProjectRow(index)"
                  >
                    删除
                  </a-button>
                </template>
              </a-table>
            </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import { getDispatchOptions, getProjectOptions, getProjectUserOptions, addDispatchInstruction, editDispatchInstruction, getDispatchInstructionById, checkCmdCode } from '../services'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: ['dispatchOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        projectOptions: [],
        userOptions: [],
        dispatchTypeOptions: [
          { label: '防汛调度', value: '1' },
          { label: '灌溉调度', value: '2' },
          { label: '生态调水', value: '3' },
        ],
        formTitle: '',
        projectColumns: [
          {
            title: '工程名称',
            dataIndex: 'projectName',
            key: 'projectName',
            scopedSlots: { customRender: 'projectName' },
          },
          {
            title: '负责人',
            dataIndex: 'projectManager',
            key: 'projectManager',
            scopedSlots: { customRender: 'projectManager' },
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            scopedSlots: { customRender: 'remark' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: 80,
            scopedSlots: { customRender: 'action' },
          },
        ],
        form: {
          cmdCode: '',
          cmdName: '',
          dispatchId: null,
          dispatchTypeCode: '',
          planEndDate: null,
          planStartDate: null,
          planTime: [],
          wardUserIds: null,
          workUserIds: [],
          items: '',
          projectId: 0,
          projectList: [],
        },
        open: false,
        rules: {
          cmdCode: [
            { required: true, message: '调度令编码不能为空', trigger: 'blur' },
            { validator: this.validateCmdCode, trigger: 'blur' }
          ],
          cmdName: [{ required: true, message: '调度令名称不能为空', trigger: 'blur' }],
          planTime: [{ required: true, message: '计划工作时间不能为空', trigger: 'change' }],
          dispatchTypeCode: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
          projectList: [{ validator: this.validateProjectList, trigger: 'change' }],
        },
        debounceTimer: null,
      }
    },
    methods: {
      // 禁用今天之前的日期
      disabledDate(current) {
        return current && current < moment().startOf('day');
      },
      
      onRangeChange(dates) {
        this.form.planStartDate = dates ? dates[0] : null
        this.form.planEndDate = dates ? dates[1] : null
      },
      
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      
      // 新增工程行
      addProjectRow() {
        this.form.projectList.push({
          projectId: null,
          wardUserId: null,
          remark: '',
          userOptions: [],
        })
      },
      
      // 删除工程行
      removeProjectRow(index) {
        this.form.projectList.splice(index, 1)
      },
      
      // 处理工程选择变化
      handleProjectSelectChange(value, index) {
        const project = this.projectOptions.find(item => item.value === value)
        if (project) {
          // 根据工程ID获取负责人选项
          getProjectUserOptions(project.dispatchProjectId).then(res => {
            if (res.success && res.data) {
              const userOptions = res.data.map(user => ({
                label: user.name,
                value: user.userId
              }))
              this.$set(this.form.projectList[index], 'userOptions', userOptions)
              this.$set(this.form.projectList[index], 'wardUserId', null)
            }
          }).catch(err => {
            console.error('获取工程负责人失败:', err)
          })
        }
      },

      // 调度方案变化时自动填充计划工作时间
      onDispatchChange(value) {
        const dispatch = this.dispatchOptions.find(item => item.value === value)
        if (dispatch && dispatch.startTime && dispatch.endTime) {
          this.form.planTime = [dispatch.startTime, dispatch.endTime]
          this.form.planStartDate = dispatch.startTime
          this.form.planEndDate = dispatch.endTime
        }
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = row.action
        this.loadInitialData()

        if (row.action == '修改' || row.action == '复制') {
          this.modalLoading = true

          // 获取详情数据
          getDispatchInstructionById(row.runCmdId).then(res => {
            if (res.success && res.data) {
              const data = res.data
              this.form = {
                runCmdId: data.runCmdId,
                cmdCode: data.cmdCode,
                cmdName: data.cmdName,
                dispatchId: data.dispatchId,
                dispatchTypeCode: data.dispatchTypeCode,
                planTime: data.planStartDate && data.planEndDate ? [data.planStartDate, data.planEndDate] : [],
                planStartDate: data.planStartDate,
                planEndDate: data.planEndDate,
                wardUserIds: data.wardUserIds,
                workUserIds: data.workUserIds || [],
                items: data.items || '',
                projectId: data.projectId || 0,
                projectList: data.projectList ? data.projectList.map(item => ({
                  projectId: item.projectId,
                  wardUserId: item.wardUserId,
                  wardUserName: item.wardUserName,
                  remark: item.remark || '',
                  userOptions: item.wardUserId && item.wardUserName ? [{ label: item.wardUserName, value: item.wardUserId }] : []
                })) : [],
              }

              if (row.action == '复制') {
                this.form.runCmdId = null
                this.form.cmdCode = ''
              }
            }
            this.modalLoading = false
          }).catch(err => {
            console.error('获取详情失败:', err)
            this.modalLoading = false
          })
        } else {
          // 新增时初始化
          this.form = {
            cmdCode: '',
            cmdName: '',
            dispatchId: null,
            dispatchTypeCode: '',
            planEndDate: null,
            planStartDate: null,
            planTime: [],
            wardUserIds: null,
            workUserIds: [],
            items: '',
            projectId: 0,
            projectList: [],
          }
        }
      },

      /** 加载初始数据 */
      loadInitialData() {
        // 加载调度方案选项
        getDispatchOptions().then(res => {
          if (res.success && res.data && res.data.data) {
            this.dispatchOptions = res.data.data.map(item => ({
              label: item.caseName,
              value: item.chSimId,
              startTime: item.startTime,
              endTime: item.endTime
            }))
          }
        }).catch(err => {
          console.error('加载调度方案选项失败:', err)
        })

        // 加载工程选项
        getProjectOptions().then(res => {
          if (res.success && res.data) {
            this.projectOptions = res.data.map(item => ({
              label: item.projectName,
              value: item.projectId,
              dispatchProjectId: item.dispatchProjectId
            }))
          }
        }).catch(err => {
          console.error('加载工程选项失败:', err)
        })

        // TODO: 加载用户选项（工作负责人和工作人员）
        this.userOptions = [
          { label: '张三', value: 1 },
          { label: '李四', value: 2 },
          { label: '王五', value: 3 },
        ]
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            // 处理时间格式
            if (this.form.planTime && this.form.planTime.length === 2) {
              this.form.planStartDate = this.form.planTime[0]
              this.form.planEndDate = this.form.planTime[1]
            }

            // 构建提交数据
            const firstProject = this.form.projectList.length > 0 ? this.form.projectList[0] : {}
            const submitData = {
              cmdCode: this.form.cmdCode,
              cmdName: this.form.cmdName,
              dispatchId: this.form.dispatchId,
              dispatchTypeCode: this.form.dispatchTypeCode,
              planStartDate: this.form.planStartDate,
              planEndDate: this.form.planEndDate,
              wardUserIds: firstProject.wardUserId || null,
              workUserIds: firstProject.wardUserId ? [firstProject.wardUserId] : [],
              items: this.form.items,
              projectId: firstProject.projectId || 0,
              projectList: this.form.projectList.map(item => ({
                projectId: item.projectId,
                wardUserId: item.wardUserId,
                remark: item.remark
              })),
              deviceOpenTimes: [
                {
                  deviceId: '1',
                  startDate: '2025-09-04 16:41:00',
                  endDate: '2025-09-06 16:41:00'
                }
              ]
            }

            // 如果是修改，添加runCmdId
            if (this.form.runCmdId) {
              submitData.runCmdId = this.form.runCmdId
            }

            const apiCall = this.form.runCmdId ? editDispatchInstruction : addDispatchInstruction

            apiCall(submitData).then(res => {
              if (res.success) {
                this.$message.success(this.form.runCmdId ? '修改成功' : '新增成功')
                this.loading = false
                this.open = false
                this.$emit('ok')
              } else {
                this.$message.error(res.message || '操作失败')
                this.loading = false
              }
            }).catch(err => {
              console.error('提交失败:', err)
              this.$message.error('操作失败')
              this.loading = false
            })
          }
        })
      },

      // 调度令编码输入防抖校验
      onCmdCodeInput() {
        if (this.debounceTimer) clearTimeout(this.debounceTimer)
        this.debounceTimer = setTimeout(() => {
          if (!this.form.cmdCode) return
          this.$refs.form.validateField('cmdCode')
        }, 2000)
      },

      // 调度令编码唯一性校验
      validateCmdCode(rule, value, callback) {
        if (!value) {
          return callback()
        }
        checkCmdCode({ cmdCode: value, runCmdId: this.form.runCmdId || null }).then(res => {
          if (res.success && !res.data) {
            callback()
          } else {
            callback(new Error('该调度令编码已存在'))
          }
        }).catch(() => {
          callback(new Error('校验失败'))
        })
      },

      // 校验工程信息列表
      validateProjectList(rule, value, callback) {
        if (!this.form.projectList || this.form.projectList.length === 0) {
          return callback(new Error('工程信息不能为空'))
        }
        for (const item of this.form.projectList) {
          if (!item.projectId) {
            return callback(new Error('请选择工程名称'))
          }
          if (!item.wardUserId) {
            return callback(new Error('请选择负责人'))
          }
        }
        callback()
      },
    }
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .project-table-container {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;

    .table-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }

  ::v-deep .ant-table-tbody > tr > td {
    padding: 8px;
  }

  ::v-deep .ant-table-thead > tr > th {
    padding: 8px;
    background-color: #f5f5f5;
  }
</style>
