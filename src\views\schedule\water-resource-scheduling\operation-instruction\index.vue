<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="操作令编号">
        <a-input v-model="queryParam.operateCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="操作日期">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.optTime"
          @change="onRangeChange"
        />
      </a-form-item>

      <a-form-item label="调度令编号">
        <a-input v-model="queryParam.cmdCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
        </VxeTable>
        <OptCmdDetail v-if="showForm" ref="formRef" @close="showForm = false" />
        <DispatchDetail v-if="dispatchShowForm" ref="dispatchFormRef" @close="dispatchShowForm = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import OptCmdDetail from './modules/OptCmdDetail.vue'
  import DispatchDetail from '../dispatch-instruction/modules/DetailModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import { getOperateCmdPage } from './services'

  export default {
    name: 'OperationInstruction',
    components: { VxeTable, VxeTableForm, OptCmdDetail, DispatchDetail },
    data() {
      return {
        isChecked: false,
        showForm: false,
        dispatchShowForm: false,
        list: [],
        tableTitle: '操作指令',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          operateCode: '',
          cmdCode: '',
          keyword: '',
          operateTask: '',
          startDate: null,
          endDate: null,
          optTime: [],
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },

        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '操作令编号',
            field: 'operateCode',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '操作任务',
            field: 'operateTask',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '关联的调度指令',
            field: 'cmdCode',
            minWidth: 140,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDispatchDetail(row)} style="color: #1890ff;">
                      {row.cmdCode}
                    </a>
                  </span>
                )
              },
            },
          },
          {
            title: '操作人',
            field: 'operateName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '监护人',
            field: 'guardianName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作时间',
            field: 'operateTime',
            minWidth: 180,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.startDate && row.endDate) {
                  return `${row.startDate} ~ ${row.endDate}`
                }
                return row.operateDate || '-'
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleOptDetail(row)}>查看</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },

    methods: {
      onRangeChange(value, dateString) {
        if (dateString.length == 0) {
          this.queryParam.startDate = null
          this.queryParam.endDate = null
          return
        }
        this.queryParam.startDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },

      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.dispatchShowForm = false
        this.loading = true
        this.selectChange({ records: [] })

        getOperateCmdPage(this.queryParam).then(res => {
          if (res.success) {
            this.list = res.data.data || []
            this.total = res.data.total || 0
          } else {
            this.$message.error(res.message || '查询失败')
          }
          this.loading = false
        }).catch(err => {
          console.error('查询列表失败:', err)
          this.$message.error('查询失败')
          this.loading = false
        })
      },

      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.operateCmdId)
        this.isChecked = !!valObj.records.length
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          operateCode: '',
          cmdCode: '',
          keyword: '',
          operateTask: '',
          startDate: null,
          endDate: null,
          optTime: [],
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 查看调度指令详情
      handleDispatchDetail(record) {
        this.dispatchShowForm = true
        // 构造调度指令对象，使用runCmdId
        const dispatchRecord = {
          runCmdId: record.runCmdId
        }
        this.$nextTick(() => this.$refs.dispatchFormRef.handle(dispatchRecord, 'view'))
      },

      // 查看操作指令详情
      handleOptDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
    },
  }
</script>

<style lang="less" scoped>
</style>