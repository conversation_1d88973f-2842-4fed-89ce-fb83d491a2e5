<template>
  <div style="flex: 1; width: 100%; height: 100%; display: flex; flex-direction: column;">
    <!-- 错误状态显示 -->
    <div v-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <!-- 结果面板 -->
    <div v-else-if="!!chSimId && !!modelId" style="flex: 1; height: 100%; width: 100%;">
      <ShowModelComponent v-if="!!dataSource.chSimId" :chSimId="chSimId" :modelId="modelId" />
    </div>

    <!-- 加载中状态 -->
    <div v-else style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-spin size="large" />
    </div>
  </div>
</template>

<script lang="jsx">
import { getChSimDetail } from '../../services'
import ShowModelComponent from '../../ShowModelComponent.vue'

export default {
  name: 'Result',
  props: ['baseInfo', 'projectFlows', 'chSimId'],
  components: {
    ShowModelComponent,
  },
  data() {
    return {
      loading: false,
      errorInfo: null,
      modelId: null
    }
  },
  watch: {
    chSimId: {
      handler(newVal) {
        if (newVal) {
          this.loadModelDetail()
        }
      },
      immediate: true
    }
  },
  created() {
    this.$emit('update:isDisabledBtn', false)
    this.errorInfo = null
  },
  methods: {
    // 加载模型详情
    async loadModelDetail() {
      if (!this.chSimId) return

      try {
        this.loading = true
        const response = await getChSimDetail({ chSimId: this.chSimId })
        console.log('模型详情响应:', response)

        if (response.data && response.data.modelId) {
          this.modelId = response.data.modelId
        } else {
          this.errorInfo = '获取模型详情失败，无法显示结果面板'
        }
      } catch (error) {
        console.error('获取模型详情失败:', error)
        this.errorInfo = '获取模型详情失败，请重试'
      } finally {
        this.loading = false
      }
    },

    save() {
      this.$emit('saveData', this.chSimId)
      // 保存后关闭弹窗
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped></style>
