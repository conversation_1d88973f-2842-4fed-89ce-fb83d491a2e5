<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="720"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">调度令编码：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdCode }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度令名称：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度类型：</label>
            <span class="common-value-text">
              {{ objDetail?.dispatchTypeName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">计划工作时间：</label>
            <span class="common-value-text">
              {{ objDetail?.planStartDate && objDetail?.planEndDate ? `${objDetail.planStartDate} ~ ${objDetail.planEndDate}` : '-' }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">工程信息</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px;height: 200px">
            <VxeTable
              ref="vxeProjectTableRef"
              :height="200"
              :isShowTableHeader="false"     
              :columns="projectColumns"
              :tableData="objDetail?.projectList || []"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>

        <template v-if="mode === 'view' && (objDetail?.statusCode === 2 || objDetail?.statusCode === 3)">
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px">
              <div class="title">审批信息</div>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">审批人：</label>
              <span class="common-value-text">
                {{ objDetail?.auditUserName }}
              </span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">审批时间：</label>
              <span class="common-value-text">
                {{ objDetail?.auditTime }}
              </span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">审批意见：</label>
              <span class="common-value-text">
                {{ objDetail?.auditResult === 1 ? '通过' : '驳回' }}
              </span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" v-if="objDetail?.remark">
            <div class="item">
              <label class="common-label-text">备注：</label>
              <span class="common-value-text">
                {{ objDetail?.remark }}
              </span>
            </div>
          </a-col>
        </template>

        <template v-if="mode === 'view' && objDetail?.statusCode === 3 && objDetail?.projectOpInfoList && objDetail.projectOpInfoList.length > 0">
          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px">
              <div class="title">工程操作信息</div>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 10px;height: 200px">
              <VxeTable
                ref="vxeProjectOpInfoTableRef"
                :height="200"
                :isShowTableHeader="false"
                :columns="projectOpInfoColumns"
                :tableData="objDetail?.projectOpInfoList || []"
                :rowConfig="{ isCurrent: true, isHover: true }"
              ></VxeTable>
            </div>
          </a-col>
        </template>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">{{ mode === 'receive' ? '取消' : '关闭' }}</a-button>
      <a-button v-if="mode === 'receive'" type="primary" @click="handleReceive" :loading="submitLoading">接收</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import moment from 'moment'
  import { getDispatchInstructionById } from '../services'

  export default {
    name: 'DetailModal',
    components: { AntModal, VxeTable },
    props: [],
    data() {
      return {
        formTitle: '调度指令详情',
        modalLoading: false,
        open: false,
        mode: 'view', // view, receive
        objDetail: {},
        submitLoading: false,
        projectColumns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '负责人',
            field: 'wardUserName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                const statusMap = {
                  0: { text: '未接收', color: '#ff4d4f' },
                  1: { text: '已接收', color: '#52c41a' }
                }
                const status = statusMap[row.recStatusCode] || { text: '未知', color: '#8c8c8c' }
                return <span style={`color: ${status.color}`}>{status.text}</span>
              },
            },
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
        ],
        auditColumns: [
          {
            title: '审批人',
            field: 'auditUser',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '审批时间',
            field: 'auditTime',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
          {
            title: '审批意见',
            field: 'auditResult',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '备注信息',
            field: 'auditRemark',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
        ],
        projectOpInfoColumns: [
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作时间',
            field: 'operateDate',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '操作人',
            field: 'operateName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '监护人',
            field: 'guardianName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
        ],
      }
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      getStatusText(status) {
        const statusMap = {
          0: '审批中',
          1: '下发中',
          2: '已驳回',
          3: '已完成'
        }
        return statusMap[status] || '未知'
      },

      getStatusColor(status) {
        const colorMap = {
          0: '#1890ff',
          1: '#52c41a',
          2: '#ff4d4f',
          3: '#8c8c8c'
        }
        return colorMap[status] || '#8c8c8c'
      },

      /** 按钮操作 */
      handle(row, mode = 'view') {
        this.open = true
        this.mode = mode
        this.modalLoading = true

        // 设置标题
        const titleMap = {
          view: '查看调度指令',
          receive: '接收调度指令',
        }
        this.formTitle = titleMap[mode] || '调度指令详情'

        // 获取详情数据
        getDispatchInstructionById(row.runCmdId).then(res => {
          if (res.success && res.data) {
            this.objDetail = res.data
          } else {
            this.$message.error(res.message || '获取详情失败')
          }
          this.modalLoading = false
        }).catch(err => {
          console.error('获取详情失败:', err)
          this.$message.error('获取详情失败')
          this.modalLoading = false
        })
      },

      handleReceive() {
        this.submitLoading = true
        
        // 模拟接收操作
        setTimeout(() => {
          // this.$message.success('接收成功')
          // 通知父组件刷新列表
          this.$emit('receiveResult', this.objDetail)
          this.cancel()
          this.submitLoading = false
        }, 1000)
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 20px;
  }
</style>
