<template>
    <div class="common-table-page">
      <div class="page-header">
        <div class="page-title">水库调度模型</div>
        <div class="page-back">
          <a-button icon="arrow-left" @click="goBack">返回</a-button>
        </div>
      </div>
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="方案编码">
          <a-input v-model="queryParam.caseCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>
        <a-form-item label="方案名称">
          <a-input v-model="queryParam.caseName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>
  
        <a-form-item label="调度模拟场景">
          <a-select allowClear v-model="queryParam.scene" placeholder="请选择" :options="sceneOptions"></a-select>
        </a-form-item>
  
        <a-form-item label="调度模式">
          <a-select
            allowClear
            v-model="queryParam.dispathMode"
            placeholder="请选择"
            :options="dispatchModelOptions"
          ></a-select>
        </a-form-item>
  
        <a-form-item label="发起人">
          <a-select
            allowClear
            v-model="queryParam.userId"
            placeholder="请选择"
            :options="createdUserOptions"
            show-search
            :filter-option="
              (input, option) => option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
            "
          ></a-select>
        </a-form-item>
  
        <template #table>
          <VxeTable
            :isBack="true"
            ref="vxeTableRef"
            tableTitle="水库调度模型"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button
                type="primary"
                icon="diff"
                :disabled="!canCompare"
                @click="handleCompare"
              >
                方案对比
              </a-button>
              <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
          <AddModal v-if="showAddModal" ref="addModalRef" @ok="onOperationComplete" @close="showAddModal = false" />
          <DetailModal
            v-if="showDetailModal"
            :simulateTypeOptions="simulateTypeOptions"
            :sceneOptions="sceneOptions"
            :dispatchModelOptions="dispatchModelOptions"
            :dispatchTypeOptions="dispatchTypeOptions"
            ref="detailModalRef"
            @ok="onOperationComplete"
            @close="showDetailModal = false"
          />
          <InWaterDetail
            v-if="showInWaterDetailModal"
            ref="inWaterDetailModalRef"
            @ok="onOperationComplete"
            @close="showInWaterDetailModal = false"
          />
          <ReportModal
            v-if="showReportModal"
            ref="reportModalRef"
            @close="showReportModal = false"
          />
          <CompareModal
            v-if="showCompareModal"
            ref="compareModalRef"
            @close="showCompareModal = false"
          />
        </template>
      </VxeTableForm>
    </div>
  </template>
  
  <script lang="jsx">
    import { getComUserList, getOptions } from '@/api/common'
    import { getResvrDispPage, deleteResvrDisp } from '../services.js'
  
    import VxeTable from '@/components/VxeTable'
    import VxeTableForm from '@/components/VxeTableForm'
    import excelExport from '@/utils/excelExport.js'
    import moment from 'moment'
    import AddModal from '../modules/AddModal/index.vue'
    import DetailModal from '../modules/DetailModal.vue'
    import InWaterDetail from '../../incoming-water-model/manual-forecast/modules/DetailModal.vue'
    import ReportModal from '../modules/ReportModal.vue'
    import CompareModal from '../modules/CompareModal/index.vue'
    import {
      dispatchModelOptions,
      dispatchTypeOptions,
      sceneOptions,
      simulateTypeOptions,
      modelStatusOptions,
    } from '../config'
  
    export default {
      name: 'dispatchModel',
      components: {
        VxeTable,
        VxeTableForm,
        AddModal,
        DetailModal,
        InWaterDetail,
        ReportModal,
        CompareModal,
      },
      data() {
        return {
          exportLoading: false,
          showAddModal: false,
          showDetailModal: false,
          showInWaterDetailModal: false,
          showReportModal: false,
          showCompareModal: false,
          dispatchModelOptions,
          dispatchTypeOptions,
          sceneOptions,
          simulateTypeOptions,
          modelStatusOptions,
          createdUserOptions: [],
  
          list: [],
          isChecked: false,
          ids: [],
          names: [],
          selectedRecords: [], // 选中的记录
          loading: false,
          total: 0,
  
          queryParam: {
            caseCode: undefined,
            caseName: undefined,
            scene: undefined,
            dispathMode: undefined,
            userId: undefined,
            pageNum: 1,
            pageSize: 10,
            sort: [],
          },
          columns: [
            { type: 'checkbox', width: 30 },
            { type: 'seq', title: '序号', width: 50 },
            {
              title: '方案编码',
              field: 'caseCode',
              minWidth: 220,
              showOverflow: 'tooltip',
            },
            {
              title: '方案名称',
              field: 'caseName',
              minWidth: 160,
              showOverflow: 'tooltip',
            },
            // {
            //   title: '调度模拟方式',
            //   field: 'simulateType',
            //   minWidth: 110,
            //   showOverflow: 'tooltip',
            //   slots: {
            //     default: ({ row, rowIndex }) => {
            //       const label = this.simulateTypeOptions.find(el => el.value == row.simulateType)?.label
            //       return row.simulateType === 1 ? <a onClick={() => this.handleInWaterDetail(row)}>{label}</a> : label
            //     },
            //   },
            // },
            {
              title: '调度方式',
              field: 'dispathType',
              minWidth: 110,
              showOverflow: 'tooltip',
              slots: {
                default: ({ row, rowIndex }) => {
                  return this.dispatchModelOptions.find(el => el.value == row.dispathType)?.label
                },
              },
            },
            {
              title: '调度模拟场景',
              field: 'scene',
              minWidth: 110,
              showOverflow: 'tooltip',
              slots: {
                default: ({ row, rowIndex }) => {
                  return this.sceneOptions.find(el => el.value == row.scene)?.label
                },
              },
            },
            // {
            //   title: '调度模式',
            //   field: 'dispathMode',
            //   minWidth: 110,
            //   showOverflow: 'tooltip',
            //   slots: {
            //     default: ({ row, rowIndex }) => {
            //       return this.dispatchModelOptions.find(el => el.value == row.dispathMode)?.label
            //     },
            //   },
            // },
            {
              title: '预报时段',
              field: 'outerId',
              minWidth: 200,
              showOverflow: 'tooltip',
              slots: {
                default: ({ row, rowIndex }) => {
                  return `${row.startTime} - ${row.endTime}`
                },
              },
            },
            {
              title: '方案生成时间',
              field: 'saveTime',
              minWidth: 170,
              showOverflow: 'tooltip',
              slots: {
                default: ({ row, rowIndex }) => {
                  return row.saveTime || '-'
                }
              }
            },
            {
              title: '发起人',
              field: 'createdUserName',
              minWidth: 80,
            },
            {
              title: '操作',
              field: 'operate',
              width: 180,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <span>
                      <a onClick={() => this.handleDetail(row)}>模型结果</a>
                      <a-divider type='vertical' />
                      <a onClick={() => this.handleDelete(row)}>删除</a>
                      {/* <a-divider type='vertical' />
                      <a onClick={() => this.handleReport(row)}>方案报告</a> */}
                    </span>
                  )
                },
              },
            },
          ],
        }
      },
      computed: {
        // 判断是否可以进行方案对比
        canCompare() {
          if (this.selectedRecords.length !== 2) return false
          // 检查两个方案的预报时段是否相同
          const [record1, record2] = this.selectedRecords
          return record1.startTime === record2.startTime && record1.endTime === record2.endTime
        }
      },
      watch: {},
      created() {
        getComUserList({}).then(res => {
          this.createdUserOptions = res?.data.map(el => ({ label: el.name, value: el.userId }))
        })
        this.getList()
      },
  
      methods: {
        goBack() {
          this.$router.go(-1)
        },
        adaptPageSizeChange(pageSize) {
          this.queryParam.pageSize = pageSize
          this.getList()
        },
        /** 查询列表 */
        getList() {
          this.showAddModal = false
          this.loading = true
          this.selectChange({ records: [] })
          getResvrDispPage(this.queryParam).then(response => {
            this.list = response.data?.data || []
            this.total = response.data?.total || 0
            this.loading = false
          })
        },
        /** 搜索按钮操作 */
        handleQuery() {
          this.queryParam.pageNum = 1
          this.getList()
        },
        /** 重置按钮操作 */
        resetQuery() {
          this.queryParam = {
            ...this.queryParam,
            caseCode: undefined,
            caseName: undefined,
            scene: undefined,
            dispathMode: undefined,
            userId: undefined,
            pageNum: 1,
            sort: [],
          }
          this.handleQuery()
        },
  
        // 分页
        handlePageChange({ currentPage, pageSize }) {
          this.queryParam.pageNum = currentPage
          this.queryParam.pageSize = pageSize
          this.getList()
        },
        // 多选框选中
        selectChange(valObj) {
          this.ids = valObj.records.map(item => item.resvrDispId)
          this.names = valObj.records.map(item => item.caseName)
          this.isChecked = !!valObj.records.length
          this.selectedRecords = valObj.records // 保存选中的记录
        },
        // 排序
        sortChange(valObj) {
          this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
          this.getList()
        },
  
        /* 新增 */
        handleAdd() {
          this.showAddModal = true
          this.$nextTick(() => this.$refs.addModalRef.handleShow())
        },
        /* 详情 */
        handleDetail(record) {
          this.showDetailModal = true
          this.$nextTick(() => this.$refs.detailModalRef.handleShow(record))
        },
        handleInWaterDetail(row) {
          this.showInWaterDetailModal = true
          this.$nextTick(() => this.$refs.inWaterDetailModalRef.handleShow({ inWaterId: row.waterFcstId }))
        },
        /* 方案报告 */
        handleReport(record) {
          this.showReportModal = true
          this.$nextTick(() => this.$refs.reportModalRef.handleShow(record))
        },
        /* 方案对比 */
        handleCompare() {
          if (!this.canCompare) {
            this.$message.warning('请选择两个预报时段相同的方案进行对比')
            return
          }
          this.showCompareModal = true
          this.$nextTick(() => this.$refs.compareModalRef.handleShow(this.selectedRecords))
        },
        /** 删除按钮操作 */
        handleDelete(row) {
          var that = this
          const resvrDispIds = row.resvrDispId ? [row.resvrDispId] : this.ids
          const names = row.caseName || this.names
  
          this.$confirm({
            title: '确认删除所选中数据?',
            content: '当前选中名称为"' + names + '"的数据',
            onOk() {
              return deleteResvrDisp({ resvrDispIds: resvrDispIds.join(',') }).then(res => {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              })
            },
            onCancel() {},
          })
        },
  
        // 操作完成后
        onOperationComplete() {
          this.getList()
        },
  
        // 导出
        handleExport() {
          return
          this.exportLoading = true
          getResvrDispPage({ ...this.queryParam, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
            this.exportLoading = false
  
            const exportColumns = [
              {
                title: '终端编码',
                field: 'caseCode',
                minWidth: 100,
              },
              {
                title: '方案名称',
                field: 'caseName',
                minWidth: 140,
              },
              {
                title: '终端简称',
                field: 'caseNameAbbr',
                minWidth: 120,
              },
              {
                title: '终端类别',
                field: 'sourceType',
                minWidth: 100,
              },
              {
                title: '终端型号',
                field: 'terminalModelNumber',
                minWidth: 120,
              },
              {
                title: '终端状态',
                field: 'terminalStatusCode',
                minWidth: 120,
              },
              {
                title: '监测指标',
                field: 'indexCodes',
                minWidth: 180,
              },
              {
                title: '传输方式',
                field: 'transmissionCode',
                minWidth: 100,
              },
              {
                title: '终端备注',
                field: 'remark',
                minWidth: 120,
              },
  
              {
                title: '运维部门',
                field: 'operationDept',
                minWidth: 100,
              },
              {
                title: '卡有效期',
                field: 'iotCardExpire',
                minWidth: 100,
              },
              {
                title: '负责人',
                field: 'responsiblePerson',
                minWidth: 100,
              },
              {
                title: '物联网卡号',
                field: 'iotCardNo',
                minWidth: 100,
              },
              {
                title: '剩余有效期',
                field: 'remainingPeriodValidity',
                minWidth: 100,
              },
              {
                title: '终端启用日期',
                field: 'enabledTime',
                minWidth: 120,
              },
              {
                title: '运营商',
                field: 'iotCardIsp',
                minWidth: 120,
              },
              {
                title: '是否自动续费',
                field: 'isRenewal',
                minWidth: 120,
              },
              {
                title: '到期提醒',
                field: 'isNotice',
                minWidth: 120,
              },
              {
                title: '运维备注',
                field: 'remarkOps',
                minWidth: 120,
              },
            ]
            const data = (res.data?.data || []).map(row => ({
              ...row,
              districtCode: this.terminalTypeOptions.find(el => el.key == row.sourceType)?.value,
              indexCodes: row.indexCodes
                .map(item => {
                  return this.monitoringIndexOptions.find(el => el.key == item)?.value
                })
                .join('、'),
  
              transmissionCode: transmissionCodeTypes[row.transmissionCode],
              terminalStatusCode: this.terminalStatusOptions.find(el => el.key == row.terminalStatusCode)?.value,
  
              isNotice: row.isNotice == 1 ? '是' : '否',
              isRenewal: row.isRenewal == 1 ? '是' : '否',
            }))
  
            excelExport(exportColumns, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
          })
        },
      },
    }
  </script>
  <style lang="less" scoped>
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 16px 0;
    margin-bottom: 5px;
    background-color: #fff;
    padding: 12px 20px;
    border-radius: 8px;
    .page-title {
      font-size: 18px;
      font-weight: bold;
    }
  }
  </style>
  