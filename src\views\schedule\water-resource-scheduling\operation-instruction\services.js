import request from '@/utils/request'
// 分页查询操作指令（列表页面使用）
export function getOperateCmdPage(data) {
  return request({
    url: '/custom/operateCmd/page',
    method: 'post',
    data
  })
}

// 分页查询操作指令（折叠面板使用，根据调度指令编码查询）
export function getOperateCmdByDispatch(data) {
  return request({
    url: '/custom/operateCmd/page',
    method: 'post',
    data
  })
}

// 根据ID获取操作指令详情
export function getOperateCmdById(operateCmdId) {
  return request({
    url: '/custom/operateCmd/get',
    method: 'post',
    params: { operateCmdId }
  })
}
