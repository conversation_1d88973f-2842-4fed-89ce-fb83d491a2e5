<template>
  <div class="common-table-page">
    <div class="page-header">
      <div class="page-title">来水预报方案</div>
      <div class="page-actions">
        <a-button icon="arrow-left" @click="goBack">返回</a-button>
      </div>
    </div>
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="方案编码">
        <a-input v-model="queryParam.caseCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="方案名称">
        <a-input v-model="queryParam.caseName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="预报方式">
        <a-select
          allowClear
          v-model="queryParam.sourceType"
          placeholder="请选择"
          :options="sourceTypeOptions"
        ></a-select>
      </a-form-item>

      <a-form-item label="预报开始时间">
        <a-range-picker
          allow-clear
          v-model="queryParam.range"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MD"
          :placeholder="['开始时间', '结束时间']"
        />
      </a-form-item>

      <a-form-item label="发起人">
        <a-select
          allowClear
          v-model="queryParam.createdUserId"
          placeholder="请选择"
          :options="createdUserOptions"
          show-search
          :filter-option="
            (input, option) => option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
          "
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          :isBack="true"
          ref="vxeTableRef"
          tableTitle=" "
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <!-- <a-button @click="handleSiteConfigure">站点配置</a-button> -->
            <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <SiteConfigureModal
          v-if="showSiteConfigureModal"
          :fcstRangeOptions="fcstRangeOptions"
          ref="siteConfigureModalRef"
          @ok="onOperationComplete"
          @close="showSiteConfigureModal = false"
        />
        <AddModal
          v-if="showAddModal"
          :fcstRangeOptions="fcstRangeOptions"
          :sceneOptions="sceneOptions"
          ref="addModalRef"
          @ok="onOperationComplete"
          @close="showAddModal = false"
          @handleAddDispatchDetail="handleAddDispatchDetail"
        />
        <DetailModal
          v-if="showDetailModal"
          ref="detailModalRef"
          @ok="onOperationComplete"
          @close="showDetailModal = false"
          @handleAddDispatchDetail="handleAddDispatchDetail"
        />
        <AddDispatchModal v-if="showAddDispatchModal" ref="addDispatchModalRef" @close="showAddDispatchModal = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getInWaterPage, deleteInWater } from '../services.js'
  import { getOptions } from '@/api/common'
  import { getComUserList } from '@/api/common'

  import AddModal from '../manual-forecast/modules/AddModal/index.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import SiteConfigureModal from '../manual-forecast/modules/SiteConfigureModal.vue'
  import DetailModal from '../manual-forecast/modules/DetailModal.vue'
  import AddDispatchModal from '../../dispatch-model/modules/AddModal/index.vue'

  import { sourceTypeOptions, sceneOptions, modelStatusOptions } from '../manual-forecast/config.js'

  export default {
    name: 'IncomingWaterModelList',
    components: {
      VxeTable,
      VxeTableForm,
      SiteConfigureModal,
      AddModal,
      DetailModal,
      AddDispatchModal,
    },
    data() {
      return {
        exportLoading: false,
        showAddModal: false,
        showDetailModal: false,
        showSiteConfigureModal: false,
        showAddDispatchModal: false,
        sourceTypeOptions,
        sceneOptions,
        modelStatusOptions,
        createdUserOptions: [],
        fcstRangeOptions: [],

        list: [],
        tableTitle: '手动预报',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          caseCode: undefined,
          caseName: undefined,
          sourceType: undefined,
          range: [],
          createdUserId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '方案编码',
            field: 'caseCode',
            minWidth: 220,
            showOverflow: 'tooltip',
          },
          {
            title: '方案名称',
            field: 'caseName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '预报方式',
            field: 'sourceType',
            minWidth: 80,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.sourceTypeOptions.find(el => el.value == row.sourceType)?.label
              },
            },
          },
          {
            title: '调度模拟场景',
            field: 'scene',
            minWidth: 110,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.sceneOptions.find(el => el.value == row.scene)?.label
              },
            },
          },
          {
            title: '预报时段',
            field: 'outerId',
            minWidth: 200,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return `${row.startTime} - ${row.endTime}`
              },
            },
          },
          // {
          //   title: '预报范围',
          //   field: 'fcstRangeName',
          //   minWidth: 110,
          //   showOverflow: 'tooltip',
          // },
          {
            title: '成果生成时间',
            field: 'saveTime',
            minWidth: 170,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.saveTime || '-'
              },
            },
          },
          {
            title: '预计总来水量(万m³)',
            field: 'inWater',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '发起人',
            field: 'createdUserName',
            minWidth: 80,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getComUserList({}).then(res => {
        this.createdUserOptions = res?.data.map(el => ({ label: el.name, value: el.userId }))
      })

      getOptions('fcstRange').then(res => {
        this.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })
    },

    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 返回上一页 */
      goBack() {
        this.$router.go(-1)
      },
      /** 查询列表 */
      getList() {
        this.showAddModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getInWaterPage({
          ...this.queryParam,
          range: undefined,
          startTime: this.queryParam.range?.[0],
          endTime: this.queryParam.range?.[1],
        }).then(response => {
          this.list = response.data?.data || []
          this.total = response.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          caseCode: undefined,
          caseName: undefined,
          sourceType: undefined,
          range: [],
          createdUserId: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.inWaterId)
        this.names = valObj.records.map(item => item.caseName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      handleSiteConfigure() {
        this.showSiteConfigureModal = true
        this.$nextTick(() => this.$refs.siteConfigureModalRef.handleShow())
      },
      /* 新增 */
      handleAdd() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.addModalRef.handleShow())
      },
      /* 详情 */
      handleDetail(record) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleShow(record))
      },
      handleAddDispatchDetail(params) {
        this.showAddDispatchModal = true
        this.$nextTick(() => this.$refs.addDispatchModalRef.handleShow(params))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const inWaterIds = row.inWaterId ? [row.inWaterId] : this.ids
        const names = row.caseName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteInWater({ inWaterIds: inWaterIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },

      // 导出
      handleExport() {
        return
        this.exportLoading = true
        getInWaterPage({ ...this.queryParam, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
          this.exportLoading = false

          const exportColumns = [
            {
              title: '终端编码',
              field: 'caseCode',
              minWidth: 100,
            },
            {
              title: '方案名称',
              field: 'caseName',
              minWidth: 140,
            },
            {
              title: '终端简称',
              field: 'caseNameAbbr',
              minWidth: 120,
            },
            {
              title: '终端类别',
              field: 'sourceType',
              minWidth: 100,
            },
            {
              title: '终端型号',
              field: 'terminalModelNumber',
              minWidth: 120,
            },
            {
              title: '终端状态',
              field: 'terminalStatusCode',
              minWidth: 120,
            },
            {
              title: '监测指标',
              field: 'indexCodes',
              minWidth: 180,
            },
            {
              title: '传输方式',
              field: 'transmissionCode',
              minWidth: 100,
            },
            {
              title: '终端备注',
              field: 'remark',
              minWidth: 120,
            },

            {
              title: '运维部门',
              field: 'operationDept',
              minWidth: 100,
            },
            {
              title: '卡有效期',
              field: 'iotCardExpire',
              minWidth: 100,
            },
            {
              title: '负责人',
              field: 'responsiblePerson',
              minWidth: 100,
            },
            {
              title: '物联网卡号',
              field: 'iotCardNo',
              minWidth: 100,
            },
            {
              title: '剩余有效期',
              field: 'remainingPeriodValidity',
              minWidth: 100,
            },
            {
              title: '终端启用日期',
              field: 'enabledTime',
              minWidth: 120,
            },
            {
              title: '运营商',
              field: 'iotCardIsp',
              minWidth: 120,
            },
            {
              title: '是否自动续费',
              field: 'isRenewal',
              minWidth: 120,
            },
            {
              title: '到期提醒',
              field: 'isNotice',
              minWidth: 120,
            },
            {
              title: '运维备注',
              field: 'remarkOps',
              minWidth: 120,
            },
          ]
          const data = (res.data?.data || []).map(row => ({
            ...row,
            districtCode: this.terminalTypeOptions.find(el => el.key == row.sourceType)?.value,
            indexCodes: row.indexCodes
              .map(item => {
                return this.monitoringIndexOptions.find(el => el.key == item)?.value
              })
              .join('、'),

            transmissionCode: transmissionCodeTypes[row.transmissionCode],
            terminalStatusCode: this.terminalStatusOptions.find(el => el.key == row.terminalStatusCode)?.value,

            isNotice: row.isNotice == 1 ? '是' : '否',
            isRenewal: row.isRenewal == 1 ? '是' : '否',
          }))

          excelExport(exportColumns, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
      /** 跳转到方案管理页面 */
      goToSchemeManagement() {
        this.$router.push('/schedule/incoming-water-model/manual-forecast')
      },
    },
  }
</script>
<style lang="less" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 5px;
  background-color: #fff;
  padding: 12px 20px;
  // border-radius: 8px;
  .page-title {
    font-size: 18px;
    font-weight: 600;
  }
  
  .page-actions {
    display: flex;
    gap: 8px;
  }
}
</style>
