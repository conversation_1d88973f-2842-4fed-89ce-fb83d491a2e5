<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="720"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作令编号：</label>
            <span class="common-value-text">
              {{ objDetail?.operateCode }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">工程名称：</label>
            <span class="common-value-text">
              {{ objDetail?.projectName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作日期：</label>
            <span class="common-value-text">
              {{ objDetail?.operateDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作人：</label>
            <span class="common-value-text">
              {{ objDetail?.operateName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">监护人：</label>
            <span class="common-value-text">
              {{ objDetail?.guardianName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作开始时间：</label>
            <span class="common-value-text">
              {{ objDetail?.startDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">操作结束时间：</label>
            <span class="common-value-text">
              {{ objDetail?.endDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">备注：</label>
            <span class="common-value-text">
              {{ objDetail?.remark }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" v-if="tableList && tableList.length > 0">
          <div style="margin-bottom: 10px">
            <div class="title">操作项目</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" v-if="tableList && tableList.length > 0">
          <div style="margin-bottom: 10px">
            <VxeTable
              ref="vxeTableRef"
              height="260"
              :isShowTableHeader="false"
              :columns="columns"
              :tableData="tableList"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">关闭</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import moment from 'moment'
  import { getOperateCmdById } from '../services'
  import { getDispatchProjectList } from '../../dispatch-project/services'

  export default {
    name: 'OptCmdDetail',
    components: { AntModal, VxeTable },
    props: [],
    data() {
      return {
        formTitle: '操作令详情',
        modalLoading: false,
        open: false,
        objDetail: {},
        tableList: [],
        projectList: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '内容',
            field: 'content',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
          // {
          //   title: '结果',
          //   field: 'status',
          //   width: 60,
          //   align: 'center',
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       return row.status == 1 ? (
          //         <a-icon type='check' style='color:green' />
          //       ) : (
          //         <a-icon type='close' style='color:red' />
          //       )
          //     },
          //   },
          // },
        ],
      }
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 按钮操作 */
      handle(row) {
        this.open = true
        this.modalLoading = true

        // 获取详情数据
        getOperateCmdById(row.operateCmdId)
          .then(res => {
            if (res.success && res.data) {
              this.objDetail = res.data
              this.tableList = res.data.operateCmdDetailsList || []
              this.getProjectList()
            } else {
              this.$message.error(res.message || '获取详情失败')
            }
            this.modalLoading = false
          })
          .catch(err => {
            console.error('获取详情失败:', err)
            this.$message.error('获取详情失败')
            this.modalLoading = false
          })
      },

      getProjectList() {
        getDispatchProjectList().then(res => {
          if (res.success) {
            this.projectList = res.data
            const project = this.projectList.find(item => item.projectId === this.objDetail.projectId)
            if (project) {
              this.objDetail.projectName = project.projectName
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: #1890ff;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  :deep(.ant-form-item) {
    margin-bottom: 20px;
  }
</style>
